// Re-export Prisma types
export * from '@prisma/client';

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: ValidationError[];
  conflicts?: SchedulingConflict[];
  pagination?: PaginationInfo;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface SchedulingConflict {
  type: 'room' | 'instructor' | 'student_group';
  resourceId: string;
  conflictingLessons: string[];
  timeSlot: {
    day: string;
    startSlot: number;
    endSlot: number;
  };
  description: string;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Request types
export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterQuery {
  search?: string;
  departmentId?: string;
  semesterId?: string;
  isActive?: boolean;
}

// Instructor availability types
export interface InstructorAvailability {
  Monday: number[];
  Tuesday: number[];
  Wednesday: number[];
  Thursday: number[];
  Friday: number[];
  Saturday: number[];
  Sunday: number[];
}

export interface InstructorPreferences {
  preferredDays?: string[];
  preferredTimeSlots?: number[];
  maxConsecutiveSlots?: number;
  minBreakBetweenLessons?: number;
}

// Scheduling types
export interface SchedulingConstraints {
  hardConstraints: HardConstraint[];
  softConstraints: SoftConstraint[];
}

export interface HardConstraint {
  type: 'uniqueness' | 'capacity' | 'room_type' | 'availability';
  description: string;
  weight: number;
}

export interface SoftConstraint {
  type: 'minimize_gaps' | 'compactness' | 'instructor_preference' | 'room_proximity';
  description: string;
  weight: number;
  priority: number;
}

export interface SchedulingRule {
  id: string;
  name: string;
  type: 'contiguity' | 'time_placement' | 'resource_usage' | 'special_combination';
  constraintType: 'hard' | 'soft';
  definition: any;
  priority: number;
  isActive: boolean;
}

// Lesson generation types
export interface LessonTemplate {
  courseId: string;
  lessonType: 'LECTURE' | 'LAB' | 'TUTORIAL' | 'COMPUTER_LAB' | 'SEMINAR' | 'WORKSHOP';
  durationInSlots: number;
  weeklyFrequency: number;
  totalSessions: number;
  requiredRoomType: 'LECTURE_HALL' | 'CLASSROOM' | 'LAB' | 'TUTORIAL_ROOM' | 'COMPUTER_LAB';
  preferredInstructorId?: string;
  studentGroups: string[];
}

export interface GeneratedLesson {
  id: string;
  courseCode: string;
  courseTitle: string;
  lessonType: string;
  studentGroupName: string;
  instructorName: string;
  durationInSlots: number;
  requiredRoomType: string;
  weeklyFrequency: number;
  totalSessions: number;
}

// Scheduling manifest types
export interface SchedulingManifest {
  sessionId: string;
  sessionName: string;
  semesterId: string;
  semesterName: string;
  generatedAt: string;
  lessonsToSchedule: ManifestLesson[];
  summary: ManifestSummary;
}

export interface ManifestLesson {
  lessonId: string;
  courseCode: string;
  courseTitle: string;
  lessonType: string;
  studentGroup: string;
  assignedInstructor: string;
  durationInSlots: number;
  requiredRoomType: string;
  weeklyFrequency: number;
  totalSessions: number;
  notes?: string;
}

export interface ManifestSummary {
  totalLessons: number;
  lessonsByType: Record<string, number>;
  lessonsByDepartment: Record<string, number>;
  instructorWorkload: Record<string, number>;
  roomRequirements: Record<string, number>;
}

// Schedule view types
export interface ScheduleView {
  type: 'instructor' | 'room' | 'student_group' | 'department';
  resourceId: string;
  resourceName: string;
  schedule: ScheduleSlot[][][]; // [week][day][slot]
}

export interface ScheduleSlot {
  lessonId?: string;
  courseCode?: string;
  courseTitle?: string;
  lessonType?: string;
  instructorName?: string;
  roomNumber?: string;
  studentGroupName?: string;
  startSlot: number;
  endSlot: number;
  isLocked?: boolean;
  conflicts?: string[];
}

// Time slot types
export interface TimeSlotInfo {
  slotNumber: number;
  startTime: string;
  endTime: string;
  displayLabel: string;
}

export interface DaySchedule {
  day: string;
  slots: ScheduleSlot[];
}

export interface WeekSchedule {
  weekNumber: number;
  days: DaySchedule[];
}

// Substitution types
export interface SubstitutionSuggestion {
  instructorId: string;
  instructorName: string;
  departmentName: string;
  availability: boolean;
  subjectExpertise: boolean;
  workloadScore: number;
  preferenceScore: number;
  overallScore: number;
  conflicts: string[];
}

export interface DailySubstitutionBulletin {
  date: string;
  substitutions: {
    originalLesson: {
      courseCode: string;
      courseTitle: string;
      timeSlot: string;
      roomNumber: string;
      studentGroup: string;
    };
    absentInstructor: string;
    substituteInstructor: string;
    reason: string;
    status: string;
  }[];
}

// Report types
export interface UtilizationReport {
  type: 'room' | 'instructor';
  period: {
    startDate: string;
    endDate: string;
  };
  data: UtilizationData[];
}

export interface UtilizationData {
  resourceId: string;
  resourceName: string;
  totalSlots: number;
  usedSlots: number;
  utilizationPercentage: number;
  peakUsageDay: string;
  averageUsagePerDay: number;
}

// File upload types
export interface FileUploadResult {
  filename: string;
  originalName: string;
  size: number;
  mimetype: string;
  path: string;
  uploadedAt: string;
}

export interface ExcelImportResult {
  success: boolean;
  totalRows: number;
  processedRows: number;
  errors: ImportError[];
  warnings: ImportWarning[];
  data: any[];
}

export interface ImportError {
  row: number;
  field: string;
  value: any;
  message: string;
}

export interface ImportWarning {
  row: number;
  field: string;
  value: any;
  message: string;
}

// System configuration types
export interface SystemSettings {
  timeSlotDuration: number; // in minutes
  dailySlotCount: number;
  workingDays: string[];
  defaultBreakDuration: number; // in slots
  maxDailyLessonsPerInstructor: number;
  maxConsecutiveLessons: number;
  schedulingTimeout: number; // in milliseconds
  optimizationLevel: 'low' | 'medium' | 'high';
}

// Authentication types
export interface UserPayload {
  id: string;
  email: string;
  role: string;
  departmentId?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: UserPayload;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

// Error types
export interface AppErrorDetails {
  code: string;
  message: string;
  statusCode: number;
  isOperational: boolean;
  timestamp: string;
  path: string;
  method: string;
}
