services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: schedule_assistant_db
    environment:
      POSTGRES_DB: schedule_assistant
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - schedule_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d schedule_assistant"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: schedule_assistant_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - schedule_network

  # Application
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: schedule_assistant_app
    environment:
      NODE_ENV: development
      DATABASE_URL: ********************************************/schedule_assistant?schema=public
      REDIS_URL: redis://redis:6379
      JWT_SECRET: dev-jwt-secret-change-this-in-production
      JWT_REFRESH_SECRET: dev-refresh-secret-change-this-in-production
      SESSION_SECRET: dev-session-secret-change-this-in-production
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - schedule_network
    command: npm run dev



volumes:
  postgres_data:
  redis_data:

networks:
  schedule_network:
    driver: bridge
