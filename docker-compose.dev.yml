version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: schedule_assistant_db
    environment:
      POSTGRES_DB: schedule_assistant
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - schedule_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: schedule_assistant_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - schedule_network

  # Application
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: schedule_assistant_app
    environment:
      NODE_ENV: development
      DATABASE_URL: ********************************************/schedule_assistant?schema=public
      REDIS_URL: redis://redis:6379
      JWT_SECRET: dev-jwt-secret-change-this-in-production
      JWT_REFRESH_SECRET: dev-refresh-secret-change-this-in-production
      SESSION_SECRET: dev-session-secret-change-this-in-production
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - schedule_network
    command: npm run dev

  # Nginx Reverse Proxy (optional for development)
  nginx:
    image: nginx:alpine
    container_name: schedule_assistant_nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.dev.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
    networks:
      - schedule_network

volumes:
  postgres_data:
  redis_data:

networks:
  schedule_network:
    driver: bridge
