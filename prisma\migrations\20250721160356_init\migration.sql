-- CreateEnum
CREATE TYPE "RoomType" AS ENUM ('LECTURE_HALL', 'CLA<PERSON>RO<PERSON>', 'LAB', 'TUTORIAL_ROOM', 'COMPUTER_LAB');

-- CreateEnum
CREATE TYPE "GroupType" AS ENUM ('FULL_LEVEL', 'GRO<PERSON>', 'SUB_GROUP');

-- C<PERSON><PERSON>num
CREATE TYPE "LessonType" AS ENUM ('LECTURE', 'LAB', 'TUTORIAL', 'COMPUTER_LAB', 'SEMINAR', 'WORKSHOP');

-- CreateEnum
CREATE TYPE "DayOfWeek" AS ENUM ('MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY');

-- CreateEnum
CREATE TYPE "ConstraintType" AS ENUM ('HARD', 'SOFT');

-- CreateEnum
CREATE TYPE "RuleType" AS ENUM ('CONTIGUITY', 'TIME_PLACEMENT', 'RESOURCE_USAGE', 'SPECIAL_COMBINATION');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "SubstitutionStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'COMPLETED');

-- CreateEnum
CREATE TYPE "SchedulingSessionStatus" AS ENUM ('DRAFT', 'MANIFEST_GENERATED', 'IN_PROGRESS', 'COMPLETED', 'PUBLISHED');

-- CreateTable
CREATE TABLE "semesters" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "semesters_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "departments" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT,
    "head_instructor_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "departments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rooms" (
    "id" TEXT NOT NULL,
    "room_number" TEXT NOT NULL,
    "max_capacity" INTEGER NOT NULL,
    "room_type" "RoomType" NOT NULL,
    "assigned_department_id" TEXT,
    "building" TEXT,
    "floor_number" INTEGER,
    "equipment_notes" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "rooms_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "instructors" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT,
    "department_id" TEXT NOT NULL,
    "availability" JSONB NOT NULL DEFAULT '{"Monday":[],"Tuesday":[],"Wednesday":[],"Thursday":[],"Friday":[],"Saturday":[],"Sunday":[]}',
    "max_daily_slots" INTEGER NOT NULL DEFAULT 20,
    "preferred_times" JSONB NOT NULL DEFAULT '{}',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "instructors_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "student_levels" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "department_id" TEXT NOT NULL,
    "expected_student_count" INTEGER NOT NULL,
    "academic_year" INTEGER NOT NULL,
    "semester_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "student_levels_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "student_groups" (
    "id" TEXT NOT NULL,
    "level_id" TEXT NOT NULL,
    "group_name" TEXT NOT NULL,
    "parent_group_id" TEXT,
    "group_type" "GroupType" NOT NULL,
    "student_count" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "student_groups_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "courses" (
    "id" TEXT NOT NULL,
    "course_code" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "department_id" TEXT NOT NULL,
    "credits" INTEGER NOT NULL DEFAULT 3,
    "description" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "courses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "lessons" (
    "id" TEXT NOT NULL,
    "course_id" TEXT NOT NULL,
    "student_group_id" TEXT NOT NULL,
    "instructor_id" TEXT NOT NULL,
    "duration_in_slots" INTEGER NOT NULL,
    "lesson_type" "LessonType" NOT NULL,
    "required_room_type" "RoomType" NOT NULL,
    "semester_id" TEXT NOT NULL,
    "weekly_frequency" INTEGER NOT NULL DEFAULT 1,
    "total_sessions" INTEGER NOT NULL DEFAULT 15,
    "is_scheduled" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "lessons_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "time_slots" (
    "slot_number" INTEGER NOT NULL,
    "start_time" TEXT NOT NULL,
    "end_time" TEXT NOT NULL,
    "display_label" TEXT NOT NULL,

    CONSTRAINT "time_slots_pkey" PRIMARY KEY ("slot_number")
);

-- CreateTable
CREATE TABLE "scheduled_lessons" (
    "id" TEXT NOT NULL,
    "lesson_id" TEXT NOT NULL,
    "room_id" TEXT NOT NULL,
    "day_of_week" "DayOfWeek" NOT NULL,
    "start_slot" INTEGER NOT NULL,
    "end_slot" INTEGER NOT NULL,
    "week_number" INTEGER NOT NULL,
    "semester_id" TEXT NOT NULL,
    "is_locked" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "scheduled_lessons_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "scheduling_rules" (
    "id" TEXT NOT NULL,
    "rule_name" TEXT NOT NULL,
    "rule_type" "RuleType" NOT NULL,
    "constraint_type" "ConstraintType" NOT NULL,
    "rule_definition" JSONB NOT NULL,
    "priority" INTEGER NOT NULL DEFAULT 1,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "semester_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "scheduling_rules_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "substitution_requests" (
    "id" TEXT NOT NULL,
    "original_scheduled_lesson_id" TEXT NOT NULL,
    "absent_instructor_id" TEXT NOT NULL,
    "substitute_instructor_id" TEXT,
    "substitution_date" TIMESTAMP(3) NOT NULL,
    "reason" TEXT,
    "status" "SubstitutionStatus" NOT NULL DEFAULT 'PENDING',
    "requested_by_id" TEXT NOT NULL,
    "approved_by_id" TEXT,
    "notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "substitution_requests_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "scheduling_sessions" (
    "id" TEXT NOT NULL,
    "semester_id" TEXT NOT NULL,
    "session_name" TEXT NOT NULL,
    "status" "SchedulingSessionStatus" NOT NULL DEFAULT 'DRAFT',
    "manifest_file_path" TEXT,
    "generation_started_at" TIMESTAMP(3),
    "generation_completed_at" TIMESTAMP(3),
    "total_lessons" INTEGER NOT NULL DEFAULT 0,
    "scheduled_lessons" INTEGER NOT NULL DEFAULT 0,
    "unscheduled_lessons" INTEGER NOT NULL DEFAULT 0,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "scheduling_sessions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "departments_name_key" ON "departments"("name");

-- CreateIndex
CREATE UNIQUE INDEX "departments_code_key" ON "departments"("code");

-- CreateIndex
CREATE UNIQUE INDEX "rooms_room_number_key" ON "rooms"("room_number");

-- CreateIndex
CREATE UNIQUE INDEX "instructors_email_key" ON "instructors"("email");

-- CreateIndex
CREATE UNIQUE INDEX "student_levels_name_department_id_semester_id_key" ON "student_levels"("name", "department_id", "semester_id");

-- CreateIndex
CREATE UNIQUE INDEX "student_groups_level_id_group_name_key" ON "student_groups"("level_id", "group_name");

-- CreateIndex
CREATE UNIQUE INDEX "courses_course_code_department_id_key" ON "courses"("course_code", "department_id");

-- AddForeignKey
ALTER TABLE "departments" ADD CONSTRAINT "departments_head_instructor_id_fkey" FOREIGN KEY ("head_instructor_id") REFERENCES "instructors"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rooms" ADD CONSTRAINT "rooms_assigned_department_id_fkey" FOREIGN KEY ("assigned_department_id") REFERENCES "departments"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "instructors" ADD CONSTRAINT "instructors_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_levels" ADD CONSTRAINT "student_levels_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_levels" ADD CONSTRAINT "student_levels_semester_id_fkey" FOREIGN KEY ("semester_id") REFERENCES "semesters"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_groups" ADD CONSTRAINT "student_groups_level_id_fkey" FOREIGN KEY ("level_id") REFERENCES "student_levels"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "student_groups" ADD CONSTRAINT "student_groups_parent_group_id_fkey" FOREIGN KEY ("parent_group_id") REFERENCES "student_groups"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "courses" ADD CONSTRAINT "courses_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lessons" ADD CONSTRAINT "lessons_course_id_fkey" FOREIGN KEY ("course_id") REFERENCES "courses"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lessons" ADD CONSTRAINT "lessons_student_group_id_fkey" FOREIGN KEY ("student_group_id") REFERENCES "student_groups"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lessons" ADD CONSTRAINT "lessons_instructor_id_fkey" FOREIGN KEY ("instructor_id") REFERENCES "instructors"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lessons" ADD CONSTRAINT "lessons_semester_id_fkey" FOREIGN KEY ("semester_id") REFERENCES "semesters"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "scheduled_lessons" ADD CONSTRAINT "scheduled_lessons_lesson_id_fkey" FOREIGN KEY ("lesson_id") REFERENCES "lessons"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "scheduled_lessons" ADD CONSTRAINT "scheduled_lessons_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "rooms"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "scheduled_lessons" ADD CONSTRAINT "scheduled_lessons_semester_id_fkey" FOREIGN KEY ("semester_id") REFERENCES "semesters"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "scheduled_lessons" ADD CONSTRAINT "scheduled_lessons_start_slot_fkey" FOREIGN KEY ("start_slot") REFERENCES "time_slots"("slot_number") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "scheduled_lessons" ADD CONSTRAINT "scheduled_lessons_end_slot_fkey" FOREIGN KEY ("end_slot") REFERENCES "time_slots"("slot_number") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "scheduling_rules" ADD CONSTRAINT "scheduling_rules_semester_id_fkey" FOREIGN KEY ("semester_id") REFERENCES "semesters"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "substitution_requests" ADD CONSTRAINT "substitution_requests_original_scheduled_lesson_id_fkey" FOREIGN KEY ("original_scheduled_lesson_id") REFERENCES "scheduled_lessons"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "substitution_requests" ADD CONSTRAINT "substitution_requests_absent_instructor_id_fkey" FOREIGN KEY ("absent_instructor_id") REFERENCES "instructors"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "substitution_requests" ADD CONSTRAINT "substitution_requests_substitute_instructor_id_fkey" FOREIGN KEY ("substitute_instructor_id") REFERENCES "instructors"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "substitution_requests" ADD CONSTRAINT "substitution_requests_requested_by_id_fkey" FOREIGN KEY ("requested_by_id") REFERENCES "instructors"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "substitution_requests" ADD CONSTRAINT "substitution_requests_approved_by_id_fkey" FOREIGN KEY ("approved_by_id") REFERENCES "instructors"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "scheduling_sessions" ADD CONSTRAINT "scheduling_sessions_semester_id_fkey" FOREIGN KEY ("semester_id") REFERENCES "semesters"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
