-- University Schedule Assistant Database Schema
-- Comprehensive schema for academic scheduling system

-- =============================================
-- CORE ENTITIES
-- =============================================

-- Semester table
CREATE TABLE semesters (
    semester_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Department table
CREATE TABLE departments (
    department_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL UNIQUE,
    code VARCHAR(10) UNIQUE,
    head_instructor_id UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Room types enum
CREATE TYPE room_type_enum AS ENUM (
    'Lecture Hall',
    'Classroom', 
    'Lab',
    'Tutorial Room',
    'Computer Lab'
);

-- Rooms table
CREATE TABLE rooms (
    room_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    room_number VARCHAR(50) NOT NULL UNIQUE,
    max_capacity INTEGER NOT NULL CHECK (max_capacity > 0),
    room_type room_type_enum NOT NULL,
    assigned_department_id UUID REFERENCES departments(department_id),
    building VARCHAR(100),
    floor_number INTEGER,
    equipment_notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Instructor availability structure (JSONB for flexible time slots)
-- Format: {"Monday": [1,2,3,4,5], "Tuesday": [], "Wednesday": [1,2,3,4,5,6,7,8]}
CREATE TABLE instructors (
    instructor_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    email VARCHAR(255) UNIQUE,
    department_id UUID NOT NULL REFERENCES departments(department_id),
    availability JSONB NOT NULL DEFAULT '{"Monday":[],"Tuesday":[],"Wednesday":[],"Thursday":[],"Friday":[],"Saturday":[],"Sunday":[]}',
    max_daily_slots INTEGER DEFAULT 20 CHECK (max_daily_slots > 0),
    preferred_times JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Student Level (e.g., "First Year Pharmacy")
CREATE TABLE student_levels (
    level_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    department_id UUID NOT NULL REFERENCES departments(department_id),
    expected_student_count INTEGER NOT NULL CHECK (expected_student_count > 0),
    academic_year INTEGER NOT NULL,
    semester_id UUID NOT NULL REFERENCES semesters(semester_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(name, department_id, semester_id)
);

-- Group types enum
CREATE TYPE group_type_enum AS ENUM (
    'Full Level',
    'Group', 
    'Sub-Group'
);

-- Student Groups (hierarchical structure)
CREATE TABLE student_groups (
    group_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    level_id UUID NOT NULL REFERENCES student_levels(level_id),
    group_name VARCHAR(100) NOT NULL,
    parent_group_id UUID REFERENCES student_groups(group_id),
    group_type group_type_enum NOT NULL,
    student_count INTEGER NOT NULL CHECK (student_count > 0),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(level_id, group_name)
);

-- Courses (abstract definition)
CREATE TABLE courses (
    course_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_code VARCHAR(20) NOT NULL,
    title VARCHAR(300) NOT NULL,
    department_id UUID NOT NULL REFERENCES departments(department_id),
    credits INTEGER DEFAULT 3,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(course_code, department_id)
);

-- Lesson types enum
CREATE TYPE lesson_type_enum AS ENUM (
    'Lecture',
    'Lab',
    'Tutorial',
    'Computer Lab',
    'Seminar',
    'Workshop'
);

-- =============================================
-- SCHEDULING CORE ENTITIES
-- =============================================

-- Lessons (concrete instances to be scheduled)
CREATE TABLE lessons (
    lesson_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID NOT NULL REFERENCES courses(course_id),
    student_group_id UUID NOT NULL REFERENCES student_groups(group_id),
    instructor_id UUID NOT NULL REFERENCES instructors(instructor_id),
    duration_in_slots INTEGER NOT NULL CHECK (duration_in_slots > 0 AND duration_in_slots <= 26),
    lesson_type lesson_type_enum NOT NULL,
    required_room_type room_type_enum NOT NULL,
    semester_id UUID NOT NULL REFERENCES semesters(semester_id),
    weekly_frequency INTEGER DEFAULT 1 CHECK (weekly_frequency > 0),
    total_sessions INTEGER DEFAULT 15 CHECK (total_sessions > 0),
    is_scheduled BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Days of week enum
CREATE TYPE day_of_week_enum AS ENUM (
    'Monday',
    'Tuesday', 
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday'
);

-- Time slots (26 slots from 9:30 AM to 4:00 PM)
CREATE TABLE time_slots (
    slot_number INTEGER PRIMARY KEY CHECK (slot_number >= 1 AND slot_number <= 26),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    display_label VARCHAR(20) NOT NULL
);

-- Scheduled lessons (the actual timetable)
CREATE TABLE scheduled_lessons (
    scheduled_lesson_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    lesson_id UUID NOT NULL REFERENCES lessons(lesson_id),
    room_id UUID NOT NULL REFERENCES rooms(room_id),
    day_of_week day_of_week_enum NOT NULL,
    start_slot INTEGER NOT NULL REFERENCES time_slots(slot_number),
    end_slot INTEGER NOT NULL REFERENCES time_slots(slot_number),
    week_number INTEGER NOT NULL CHECK (week_number > 0),
    semester_id UUID NOT NULL REFERENCES semesters(semester_id),
    is_locked BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CHECK (end_slot >= start_slot),
    CHECK (end_slot - start_slot + 1 = (SELECT duration_in_slots FROM lessons WHERE lesson_id = scheduled_lessons.lesson_id))
);

-- =============================================
-- CONSTRAINTS AND RULES SYSTEM
-- =============================================

-- Constraint types enum
CREATE TYPE constraint_type_enum AS ENUM (
    'Hard',
    'Soft'
);

-- Rule types enum
CREATE TYPE rule_type_enum AS ENUM (
    'Contiguity',
    'Time_Placement',
    'Resource_Usage',
    'Special_Combination'
);

-- Scheduling constraints and rules
CREATE TABLE scheduling_rules (
    rule_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rule_name VARCHAR(200) NOT NULL,
    rule_type rule_type_enum NOT NULL,
    constraint_type constraint_type_enum NOT NULL,
    rule_definition JSONB NOT NULL,
    priority INTEGER DEFAULT 1 CHECK (priority > 0),
    is_active BOOLEAN DEFAULT true,
    semester_id UUID REFERENCES semesters(semester_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- SUBSTITUTION MANAGEMENT
-- =============================================

-- Substitution requests
CREATE TABLE substitution_requests (
    substitution_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    original_scheduled_lesson_id UUID NOT NULL REFERENCES scheduled_lessons(scheduled_lesson_id),
    absent_instructor_id UUID NOT NULL REFERENCES instructors(instructor_id),
    substitute_instructor_id UUID REFERENCES instructors(instructor_id),
    substitution_date DATE NOT NULL,
    reason TEXT,
    status VARCHAR(20) DEFAULT 'Pending' CHECK (status IN ('Pending', 'Approved', 'Rejected', 'Completed')),
    requested_by UUID NOT NULL REFERENCES instructors(instructor_id),
    approved_by UUID REFERENCES instructors(instructor_id),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- WORKFLOW AND MANIFEST MANAGEMENT
-- =============================================

-- Scheduling sessions (workflow tracking)
CREATE TABLE scheduling_sessions (
    session_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    semester_id UUID NOT NULL REFERENCES semesters(semester_id),
    session_name VARCHAR(200) NOT NULL,
    status VARCHAR(20) DEFAULT 'Draft' CHECK (status IN ('Draft', 'Manifest_Generated', 'In_Progress', 'Completed', 'Published')),
    manifest_file_path VARCHAR(500),
    generation_started_at TIMESTAMP,
    generation_completed_at TIMESTAMP,
    total_lessons INTEGER DEFAULT 0,
    scheduled_lessons INTEGER DEFAULT 0,
    unscheduled_lessons INTEGER DEFAULT 0,
    created_by VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Core entity indexes
CREATE INDEX idx_instructors_department ON instructors(department_id);
CREATE INDEX idx_instructors_active ON instructors(is_active);
CREATE INDEX idx_rooms_type ON rooms(room_type);
CREATE INDEX idx_rooms_department ON rooms(assigned_department_id);
CREATE INDEX idx_student_groups_level ON student_groups(level_id);
CREATE INDEX idx_student_groups_parent ON student_groups(parent_group_id);
CREATE INDEX idx_courses_department ON courses(department_id);
CREATE INDEX idx_lessons_course ON lessons(course_id);
CREATE INDEX idx_lessons_group ON lessons(student_group_id);
CREATE INDEX idx_lessons_instructor ON lessons(instructor_id);
CREATE INDEX idx_lessons_semester ON lessons(semester_id);

-- Scheduling indexes
CREATE INDEX idx_scheduled_lessons_lesson ON scheduled_lessons(lesson_id);
CREATE INDEX idx_scheduled_lessons_room ON scheduled_lessons(room_id);
CREATE INDEX idx_scheduled_lessons_day_slot ON scheduled_lessons(day_of_week, start_slot, end_slot);
CREATE INDEX idx_scheduled_lessons_semester ON scheduled_lessons(semester_id);
CREATE INDEX idx_scheduled_lessons_week ON scheduled_lessons(week_number);

-- Substitution indexes
CREATE INDEX idx_substitutions_original_lesson ON substitution_requests(original_scheduled_lesson_id);
CREATE INDEX idx_substitutions_date ON substitution_requests(substitution_date);
CREATE INDEX idx_substitutions_status ON substitution_requests(status);

-- =============================================
-- FOREIGN KEY CONSTRAINTS
-- =============================================

-- Add foreign key for department head
ALTER TABLE departments ADD CONSTRAINT fk_departments_head
    FOREIGN KEY (head_instructor_id) REFERENCES instructors(instructor_id);

-- =============================================
-- INITIAL DATA POPULATION
-- =============================================

-- Insert time slots (26 slots from 9:30 AM to 4:00 PM)
INSERT INTO time_slots (slot_number, start_time, end_time, display_label) VALUES
(1, '09:30:00', '09:45:00', '9:30-9:45'),
(2, '09:45:00', '10:00:00', '9:45-10:00'),
(3, '10:00:00', '10:15:00', '10:00-10:15'),
(4, '10:15:00', '10:30:00', '10:15-10:30'),
(5, '10:30:00', '10:45:00', '10:30-10:45'),
(6, '10:45:00', '11:00:00', '10:45-11:00'),
(7, '11:00:00', '11:15:00', '11:00-11:15'),
(8, '11:15:00', '11:30:00', '11:15-11:30'),
(9, '11:30:00', '11:45:00', '11:30-11:45'),
(10, '11:45:00', '12:00:00', '11:45-12:00'),
(11, '12:00:00', '12:15:00', '12:00-12:15'),
(12, '12:15:00', '12:30:00', '12:15-12:30'),
(13, '12:30:00', '12:45:00', '12:30-12:45'),
(14, '12:45:00', '13:00:00', '12:45-1:00'),
(15, '13:00:00', '13:15:00', '1:00-1:15'),
(16, '13:15:00', '13:30:00', '1:15-1:30'),
(17, '13:30:00', '13:45:00', '1:30-1:45'),
(18, '13:45:00', '14:00:00', '1:45-2:00'),
(19, '14:00:00', '14:15:00', '2:00-2:15'),
(20, '14:15:00', '14:30:00', '2:15-2:30'),
(21, '14:30:00', '14:45:00', '2:30-2:45'),
(22, '14:45:00', '15:00:00', '2:45-3:00'),
(23, '15:00:00', '15:15:00', '3:00-3:15'),
(24, '15:15:00', '15:30:00', '3:15-3:30'),
(25, '15:30:00', '15:45:00', '3:30-3:45'),
(26, '15:45:00', '16:00:00', '3:45-4:00');
