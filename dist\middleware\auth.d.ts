import { Request, Response, NextFunction } from 'express';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                email: string;
                role: string;
                departmentId?: string;
            };
        }
    }
}
export interface JWTPayload {
    id: string;
    email: string;
    role: string;
    departmentId?: string;
    iat: number;
    exp: number;
}
export declare const authMiddleware: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const requireRole: (roles: string[]) => (req: Request, res: Response, next: NextFunction) => void;
export declare const requireDepartment: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=auth.d.ts.map