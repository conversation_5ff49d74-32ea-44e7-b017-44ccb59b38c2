# University Schedule Assistant - System Architecture

## Overview
The University Schedule Assistant is a comprehensive web-based application designed to automate and manage academic scheduling. The system follows a microservices-inspired architecture with clear separation of concerns.

## High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        CLIENT LAYER                             │
├─────────────────────────────────────────────────────────────────┤
│  React Frontend App  │  Mobile App  │  Public Schedule Viewer   │
│  (Admin Interface)   │  (Future)    │  (Read-only)              │
└─────────────────────────────────────────────────────────────────┘
                                │
                                │ HTTPS/REST API
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      API GATEWAY LAYER                          │
├─────────────────────────────────────────────────────────────────┤
│  • Authentication & Authorization (JWT)                         │
│  • Rate Limiting & Request Validation                          │
│  • API Versioning & Documentation                              │
│  • CORS & Security Headers                                     │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    APPLICATION LAYER                            │
├─────────────────────────────────────────────────────────────────┤
│  Core API Service (Node.js/Express)                            │
│  ├── Entity Management Module                                   │
│  ├── Workflow Management Module                                 │
│  ├── Scheduling Engine Module                                   │
│  ├── Substitution Management Module                             │
│  ├── Reporting & Export Module                                  │
│  └── File Processing Module                                     │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                     BUSINESS LOGIC LAYER                        │
├─────────────────────────────────────────────────────────────────┤
│  Scheduling Engine (Core Algorithm)                            │
│  ├── Constraint Solver                                         │
│  ├── Optimization Engine                                       │
│  ├── Conflict Detection                                        │
│  └── Rule Evaluation Engine                                    │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      DATA ACCESS LAYER                          │
├─────────────────────────────────────────────────────────────────┤
│  ORM/Query Builder (Prisma/TypeORM)                            │
│  ├── Entity Repositories                                       │
│  ├── Transaction Management                                    │
│  ├── Connection Pooling                                        │
│  └── Migration Management                                      │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                       STORAGE LAYER                             │
├─────────────────────────────────────────────────────────────────┤
│  PostgreSQL Database  │  Redis Cache  │  File Storage (S3/Local)│
│  (Primary Data)       │  (Sessions)   │  (Excel Files, Reports) │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Frontend Application (React)
**Technology Stack:**
- React 18 with TypeScript
- Material-UI or Ant Design for components
- React Query for state management and caching
- React Router for navigation
- React DnD for drag-and-drop scheduling interface

**Key Features:**
- Responsive admin dashboard
- Interactive timetable grid with drag-and-drop
- Excel file upload/download interface
- Real-time conflict validation
- Multi-step workflow wizard
- Substitution management interface

### 2. API Gateway & Authentication
**Technology Stack:**
- Express.js middleware
- JWT for authentication
- bcrypt for password hashing
- express-rate-limit for rate limiting
- helmet for security headers

**Responsibilities:**
- User authentication and authorization
- Request validation and sanitization
- API versioning and documentation (Swagger)
- Rate limiting and security enforcement

### 3. Core API Service
**Technology Stack:**
- Node.js with Express.js
- TypeScript for type safety
- Prisma ORM for database operations
- Joi/Zod for request validation
- Winston for logging

**Module Structure:**
```
src/
├── controllers/          # Request handlers
├── services/            # Business logic
├── repositories/        # Data access layer
├── middleware/          # Custom middleware
├── utils/              # Utility functions
├── types/              # TypeScript definitions
└── config/             # Configuration files
```

### 4. Scheduling Engine
**Technology Stack:**
- Custom constraint satisfaction algorithm
- Priority queue for optimization
- Graph algorithms for conflict detection
- Worker threads for heavy computations

**Core Algorithms:**
- **Constraint Satisfaction Problem (CSP) Solver**
- **Backtracking with Forward Checking**
- **Heuristic-based optimization**
- **Conflict detection and resolution**

### 5. Database Layer
**Primary Database: PostgreSQL**
- ACID compliance for data integrity
- Advanced indexing for performance
- JSON support for flexible constraint storage
- Full-text search capabilities

**Caching Layer: Redis**
- Session storage
- Frequently accessed schedule data
- Real-time conflict checking cache
- Background job queue

### 6. File Processing Service
**Technology Stack:**
- ExcelJS for Excel file manipulation
- Multer for file uploads
- Sharp for image processing (future)
- PDF generation libraries

## Data Flow Architecture

### 1. Scheduling Workflow Data Flow
```
User Input → Validation → Database Storage → Manifest Generation → 
Excel Download → User Review → Upload Modified → Scheduling Engine → 
Conflict Resolution → Final Schedule → Publishing
```

### 2. Real-time Conflict Detection
```
User Action → Frontend Validation → API Request → 
Constraint Checking → Database Query → Conflict Analysis → 
Response with Conflicts → UI Update
```

### 3. Substitution Management Flow
```
Absence Request → Availability Check → Substitute Suggestions → 
Approval Workflow → Schedule Update → Notification → 
Daily Bulletin Generation
```

## Security Architecture

### Authentication & Authorization
- JWT-based authentication
- Role-based access control (RBAC)
- API key authentication for external integrations
- Session management with Redis

### Data Security
- Input validation and sanitization
- SQL injection prevention (ORM)
- XSS protection
- CSRF protection
- Rate limiting and DDoS protection

### File Security
- File type validation
- Virus scanning for uploads
- Secure file storage with access controls
- Audit logging for file operations

## Performance Considerations

### Database Optimization
- Proper indexing strategy
- Query optimization
- Connection pooling
- Read replicas for reporting

### Caching Strategy
- Redis for session and frequently accessed data
- Application-level caching for static data
- CDN for static assets
- Browser caching for API responses

### Scalability
- Horizontal scaling capability
- Load balancing support
- Microservices-ready architecture
- Background job processing

## Deployment Architecture

### Development Environment
```
Docker Compose Setup:
├── app (Node.js API)
├── frontend (React dev server)
├── database (PostgreSQL)
├── cache (Redis)
└── nginx (Reverse proxy)
```

### Production Environment
```
Cloud Infrastructure:
├── Load Balancer (AWS ALB/Nginx)
├── Application Servers (Auto-scaling group)
├── Database (RDS PostgreSQL with read replicas)
├── Cache (ElastiCache Redis)
├── File Storage (S3/CloudFront)
└── Monitoring (CloudWatch/Prometheus)
```

## Monitoring & Observability

### Application Monitoring
- Health check endpoints
- Performance metrics collection
- Error tracking and alerting
- User activity analytics

### Infrastructure Monitoring
- Server resource monitoring
- Database performance monitoring
- Cache hit rate monitoring
- Network latency tracking

## Integration Points

### External Systems
- University Student Information System (SIS)
- Learning Management System (LMS)
- Email notification service
- Calendar systems (Google Calendar, Outlook)

### API Integrations
- RESTful API for external access
- Webhook support for real-time updates
- Export capabilities for third-party systems
- Import from existing scheduling systems

## Technology Stack Summary

**Backend:**
- Runtime: Node.js 18+
- Framework: Express.js
- Language: TypeScript
- Database: PostgreSQL 14+
- Cache: Redis 6+
- ORM: Prisma
- Authentication: JWT
- File Processing: ExcelJS

**Frontend:**
- Framework: React 18
- Language: TypeScript
- UI Library: Material-UI/Ant Design
- State Management: React Query
- Build Tool: Vite
- Testing: Jest + React Testing Library

**DevOps:**
- Containerization: Docker
- Orchestration: Docker Compose (dev), Kubernetes (prod)
- CI/CD: GitHub Actions
- Monitoring: Prometheus + Grafana
- Logging: Winston + ELK Stack
