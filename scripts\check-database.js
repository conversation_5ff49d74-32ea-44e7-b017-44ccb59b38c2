#!/usr/bin/env node

/**
 * Database Connection Checker
 * Simple script to verify database connectivity
 */

const { execSync } = require('child_process');

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function checkDatabase() {
  log('\n🔍 Checking database connection...', 'blue');
  
  try {
    // Try to connect using Prisma
    execSync('npx prisma db push --preview-feature --accept-data-loss', { 
      stdio: 'pipe',
      timeout: 10000 
    });
    log('✅ Database connection successful!', 'green');
    return true;
  } catch (error) {
    log('❌ Database connection failed', 'red');
    
    // Check if it's a Docker container issue
    try {
      execSync('docker ps --filter name=schedule_assistant_db --format "table {{.Names}}\t{{.Status}}"', { stdio: 'pipe' });
      log('🐳 Docker container found, checking status...', 'yellow');
      
      const status = execSync('docker exec schedule_assistant_db pg_isready -U postgres -d schedule_assistant', { stdio: 'pipe' });
      log('✅ Docker database is ready!', 'green');
      return true;
    } catch (dockerError) {
      log('❌ Docker database not ready or not running', 'red');
    }
    
    return false;
  }
}

async function suggestSolutions() {
  log('\n💡 Suggested solutions:', 'yellow');
  log('1. Start database with Docker: npm run docker:db', 'blue');
  log('2. Install PostgreSQL locally', 'blue');
  log('3. Use a cloud database (Supabase, Railway)', 'blue');
  log('4. Run: npm run init (for guided setup)', 'blue');
}

if (require.main === module) {
  checkDatabase().then(success => {
    if (!success) {
      suggestSolutions();
      process.exit(1);
    }
  });
}

module.exports = { checkDatabase };
