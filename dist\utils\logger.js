"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logUserAction = exports.logScheduling = exports.logQuery = exports.logPerformance = exports.logDebug = exports.logWarn = exports.logInfo = exports.logError = exports.loggerStream = exports.logger = void 0;
const winston_1 = __importDefault(require("winston"));
const winston_daily_rotate_file_1 = __importDefault(require("winston-daily-rotate-file"));
const environment_1 = require("@/config/environment");
const levels = {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3,
};
const colors = {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    debug: 'blue',
};
winston_1.default.addColors(colors);
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.colorize({ all: true }), winston_1.default.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaString = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level}]: ${message} ${metaString}`;
}));
const fileFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json());
const transports = [];
if (environment_1.config.nodeEnv === 'development') {
    transports.push(new winston_1.default.transports.Console({
        level: environment_1.config.logging.level,
        format: consoleFormat,
    }));
}
if (environment_1.config.nodeEnv === 'production') {
    transports.push(new winston_daily_rotate_file_1.default({
        filename: `${environment_1.config.logging.filePath}/error-%DATE%.log`,
        datePattern: 'YYYY-MM-DD',
        level: 'error',
        format: fileFormat,
        maxSize: environment_1.config.logging.maxSize,
        maxFiles: environment_1.config.logging.maxFiles,
        zippedArchive: true,
    }));
    transports.push(new winston_daily_rotate_file_1.default({
        filename: `${environment_1.config.logging.filePath}/combined-%DATE%.log`,
        datePattern: 'YYYY-MM-DD',
        level: environment_1.config.logging.level,
        format: fileFormat,
        maxSize: environment_1.config.logging.maxSize,
        maxFiles: environment_1.config.logging.maxFiles,
        zippedArchive: true,
    }));
}
exports.logger = winston_1.default.createLogger({
    levels,
    level: environment_1.config.logging.level,
    format: fileFormat,
    transports,
    exitOnError: false,
});
if (environment_1.config.nodeEnv === 'production' && process.env.ENABLE_CONSOLE_LOGS === 'true') {
    exports.logger.add(new winston_1.default.transports.Console({
        level: 'info',
        format: consoleFormat,
    }));
}
exports.loggerStream = {
    write: (message) => {
        exports.logger.info(message.trim());
    },
};
const logError = (message, error, meta) => {
    exports.logger.error(message, {
        error: error ? {
            name: error.name,
            message: error.message,
            stack: error.stack,
        } : undefined,
        ...meta,
    });
};
exports.logError = logError;
const logInfo = (message, meta) => {
    exports.logger.info(message, meta);
};
exports.logInfo = logInfo;
const logWarn = (message, meta) => {
    exports.logger.warn(message, meta);
};
exports.logWarn = logWarn;
const logDebug = (message, meta) => {
    exports.logger.debug(message, meta);
};
exports.logDebug = logDebug;
const logPerformance = (operation, startTime, meta) => {
    const duration = Date.now() - startTime;
    exports.logger.info(`Performance: ${operation} completed in ${duration}ms`, {
        operation,
        duration,
        ...meta,
    });
};
exports.logPerformance = logPerformance;
const logQuery = (query, params, duration) => {
    if (environment_1.config.nodeEnv === 'development') {
        exports.logger.debug('Database Query', {
            query,
            params,
            duration: duration ? `${duration}ms` : undefined,
        });
    }
};
exports.logQuery = logQuery;
const logScheduling = (operation, meta) => {
    exports.logger.info(`Scheduling: ${operation}`, {
        operation,
        timestamp: new Date().toISOString(),
        ...meta,
    });
};
exports.logScheduling = logScheduling;
const logUserAction = (userId, action, resource, meta) => {
    exports.logger.info('User Action', {
        userId,
        action,
        resource,
        timestamp: new Date().toISOString(),
        ...meta,
    });
};
exports.logUserAction = logUserAction;
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map