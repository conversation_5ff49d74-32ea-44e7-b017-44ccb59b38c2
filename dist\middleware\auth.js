"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireDepartment = exports.requireRole = exports.authMiddleware = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const environment_1 = require("@/config/environment");
const errorHandler_1 = require("@/middleware/errorHandler");
const authMiddleware = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new errorHandler_1.AuthenticationError('No token provided');
        }
        const token = authHeader.substring(7);
        const decoded = jsonwebtoken_1.default.verify(token, environment_1.config.jwt.secret);
        req.user = {
            id: decoded.id,
            email: decoded.email,
            role: decoded.role,
            departmentId: decoded.departmentId,
        };
        next();
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            next(new errorHandler_1.AuthenticationError('Invalid token'));
        }
        else if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            next(new errorHandler_1.AuthenticationError('Token expired'));
        }
        else {
            next(error);
        }
    }
};
exports.authMiddleware = authMiddleware;
const requireRole = (roles) => {
    return (req, res, next) => {
        if (!req.user) {
            throw new errorHandler_1.AuthenticationError('Authentication required');
        }
        if (!roles.includes(req.user.role)) {
            throw new errorHandler_1.AuthorizationError('Insufficient permissions');
        }
        next();
    };
};
exports.requireRole = requireRole;
const requireDepartment = (req, res, next) => {
    if (!req.user) {
        throw new errorHandler_1.AuthenticationError('Authentication required');
    }
    if (req.user.role === 'admin') {
        return next();
    }
    const departmentId = req.params.departmentId || req.body.departmentId;
    if (departmentId && req.user.departmentId !== departmentId) {
        throw new errorHandler_1.AuthorizationError('Access denied to this department');
    }
    next();
};
exports.requireDepartment = requireDepartment;
//# sourceMappingURL=auth.js.map