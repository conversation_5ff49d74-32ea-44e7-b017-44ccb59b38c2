{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AACA,2CAAwC;AACxC,sDAA8C;AAG9C,MAAa,QAAS,SAAQ,KAAK;IAKjC,YAAY,OAAe,EAAE,UAAkB,EAAE,aAAa,GAAG,IAAI,EAAE,IAAa;QAClF,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAbD,4BAaC;AAGD,MAAa,eAAgB,SAAQ,QAAQ;IAG3C,YAAY,OAAe,EAAE,SAAgB,EAAE;QAC7C,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;CACF;AAPD,0CAOC;AAGD,MAAa,uBAAwB,SAAQ,QAAQ;IAGnD,YAAY,OAAe,EAAE,YAAmB,EAAE;QAChD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,qBAAqB,CAAC,CAAC;QACjD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;CACF;AAPD,0DAOC;AAGD,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,OAAe,EAAE,aAAqB;QAChD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;QAC5C,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QACnC,CAAC;IACH,CAAC;CACF;AAPD,sCAOC;AAGD,MAAa,mBAAoB,SAAQ,QAAQ;IAC/C,YAAY,UAAkB,uBAAuB;QACnD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,sBAAsB,CAAC,CAAC;IACpD,CAAC;CACF;AAJD,kDAIC;AAGD,MAAa,kBAAmB,SAAQ,QAAQ;IAC9C,YAAY,UAAkB,eAAe;QAC3C,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,qBAAqB,CAAC,CAAC;IACnD,CAAC;CACF;AAJD,gDAIC;AAGD,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,QAAgB;QAC1B,KAAK,CAAC,GAAG,QAAQ,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;IACzD,CAAC;CACF;AAJD,sCAIC;AAGD,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IACxC,CAAC;CACF;AAJD,sCAIC;AAGD,MAAa,cAAe,SAAQ,QAAQ;IAC1C,YAAY,UAAkB,mBAAmB;QAC/C,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,qBAAqB,CAAC,CAAC;IACnD,CAAC;CACF;AAJD,wCAIC;AAiBD,MAAM,iBAAiB,GAAG,CAAC,KAAU,EAAY,EAAE;IACjD,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,KAAK,OAAO;YACV,OAAO,IAAI,aAAa,CAAC,wCAAwC,CAAC,CAAC;QACrE,KAAK,OAAO;YACV,OAAO,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC;QACrC,KAAK,OAAO;YACV,OAAO,IAAI,eAAe,CAAC,+BAA+B,CAAC,CAAC;QAC9D,KAAK,OAAO;YACV,OAAO,IAAI,eAAe,CAAC,uBAAuB,CAAC,CAAC;QACtD;YACE,OAAO,IAAI,aAAa,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;IACjE,CAAC;AACH,CAAC,CAAC;AAGF,MAAM,cAAc,GAAG,CAAC,KAAU,EAAY,EAAE;IAC9C,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACvC,OAAO,IAAI,mBAAmB,CAAC,eAAe,CAAC,CAAC;IAClD,CAAC;IACD,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACvC,OAAO,IAAI,mBAAmB,CAAC,eAAe,CAAC,CAAC;IAClD,CAAC;IACD,OAAO,IAAI,mBAAmB,CAAC,uBAAuB,CAAC,CAAC;AAC1D,CAAC,CAAC;AAGF,MAAM,qBAAqB,GAAG,CAAC,KAAU,EAAY,EAAE;IACrD,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;QAElB,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC;YACjD,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YAC5B,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,KAAK,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK;SAC7B,CAAC,CAAC,CAAC;QACJ,OAAO,IAAI,eAAe,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;IAC1D,CAAC;IACD,OAAO,IAAI,eAAe,CAAC,mBAAmB,CAAC,CAAC;AAClD,CAAC,CAAC;AAGF,MAAM,iBAAiB,GAAG,CAAC,KAAe,EAAE,GAAY,EAAE,GAAa,EAAQ,EAAE;IAC/E,MAAM,aAAa,GAAkB;QACnC,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI;QAC7B,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,IAAI,EAAE,GAAG,CAAC,WAAW;QACrB,MAAM,EAAE,GAAG,CAAC,MAAM;KACnB,CAAC;IAGF,IAAI,KAAK,YAAY,eAAe,EAAE,CAAC;QACrC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IACtC,CAAC;IAED,IAAI,KAAK,YAAY,uBAAuB,EAAE,CAAC;QAC7C,aAAa,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;IAC5C,CAAC;IAGD,IAAI,oBAAM,CAAC,OAAO,KAAK,aAAa,EAAE,CAAC;QACrC,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IACpC,CAAC;IAED,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACnD,CAAC,CAAC;AAGK,MAAM,YAAY,GAAG,CAC1B,KAAY,EACZ,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,IAAI,QAAkB,CAAC;IAGvB,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;QAC9B,QAAQ,GAAG,KAAK,CAAC;IACnB,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC5C,QAAQ,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;QACxF,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QAC5C,QAAQ,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;SAAM,CAAC;QAEN,QAAQ,GAAG,IAAI,QAAQ,CACrB,oBAAM,CAAC,OAAO,KAAK,YAAY,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,EACzE,GAAG,EACH,KAAK,CACN,CAAC;IACJ,CAAC;IAGD,eAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE;QAC7B,KAAK,EAAE;YACL,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB;QACD,OAAO,EAAE;YACP,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,WAAW;YACpB,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;SACjC;QACD,UAAU,EAAE,QAAQ,CAAC,UAAU;QAC/B,aAAa,EAAE,QAAQ,CAAC,aAAa;KACtC,CAAC,CAAC;IAGH,iBAAiB,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxC,CAAC,CAAC;AA7CW,QAAA,YAAY,gBA6CvB;AAGK,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB;AAGK,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACvF,MAAM,KAAK,GAAG,IAAI,aAAa,CAAC,SAAS,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;IAC5D,IAAI,CAAC,KAAK,CAAC,CAAC;AACd,CAAC,CAAC;AAHW,QAAA,eAAe,mBAG1B;AAEF,kBAAe,oBAAY,CAAC"}