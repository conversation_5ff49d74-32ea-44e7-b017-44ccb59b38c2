const { execSync, spawn } = require('child_process');
const fs = require('fs');

console.log('🚀 Starting University Schedule Assistant...\n');

// Step 1: Clean up any existing containers
console.log('1️⃣ Cleaning up...');
try {
  // Stop all containers using port 5432 and 6379
  execSync('docker stop postgres_db redis_cache schedule_assistant_db schedule_assistant_redis 2>nul', { stdio: 'pipe' });
  execSync('docker rm postgres_db redis_cache schedule_assistant_db schedule_assistant_redis 2>nul', { stdio: 'pipe' });

  // Also stop any containers using these ports
  try {
    const containers = execSync('docker ps --format "{{.Names}}" --filter "publish=5432" --filter "publish=6379"', { encoding: 'utf8', stdio: 'pipe' });
    if (containers.trim()) {
      execSync(`docker stop ${containers.trim().split('\n').join(' ')}`, { stdio: 'pipe' });
      execSync(`docker rm ${containers.trim().split('\n').join(' ')}`, { stdio: 'pipe' });
    }
  } catch (e) {
    // Ignore errors
  }

  console.log('✅ Cleanup complete');
} catch (e) {
  // Ignore errors - containers might not exist
  console.log('✅ Cleanup complete');
}

// Step 2: Start database
console.log('2️⃣ Starting database...');
try {
  execSync('docker run -d --name postgres_db -e POSTGRES_DB=schedule_assistant -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=postgres -p 5432:5432 postgres:15-alpine', { stdio: 'inherit' });
  console.log('✅ PostgreSQL started');
} catch (e) {
  console.log('❌ Failed to start PostgreSQL');
  process.exit(1);
}

// Step 3: Start Redis
console.log('3️⃣ Starting Redis...');
try {
  execSync('docker run -d --name redis_cache -p 6379:6379 redis:7-alpine', { stdio: 'inherit' });
  console.log('✅ Redis started');
} catch (e) {
  console.log('❌ Failed to start Redis');
  process.exit(1);
}

// Step 4: Wait for database
console.log('4️⃣ Waiting for database to be ready...');
let ready = false;
for (let i = 0; i < 30; i++) {
  try {
    execSync('docker exec postgres_db pg_isready -U postgres -d schedule_assistant', { stdio: 'pipe' });
    ready = true;
    break;
  } catch (e) {
    process.stdout.write('.');
    execSync('timeout 2 2>nul || ping 127.0.0.1 -n 3 >nul', { stdio: 'pipe' });
  }
}

if (!ready) {
  console.log('\n❌ Database not ready after 60 seconds');
  process.exit(1);
}
console.log('\n✅ Database is ready!');

// Step 5: Setup database
console.log('5️⃣ Setting up database...');
try {
  execSync('npx prisma generate', { stdio: 'inherit' });
  execSync('npx prisma migrate dev --name init', { stdio: 'inherit' });
  console.log('✅ Database setup complete');
} catch (e) {
  console.log('⚠️ Database setup had issues, but continuing...');
}

// Step 6: Start the app
console.log('6️⃣ Starting the application...');
console.log('\n🎉 Application starting!');
console.log('📍 API: http://localhost:3000');
console.log('📖 Docs: http://localhost:3000/api-docs');
console.log('❤️ Health: http://localhost:3000/health');
console.log('\nPress Ctrl+C to stop\n');

// Start the development server
const app = spawn('npx', ['nodemon', '--exec', 'npx ts-node src/server.ts'], {
  stdio: 'inherit',
  shell: true
});

// Handle shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down...');
  app.kill();
  process.exit(0);
});

app.on('exit', (code) => {
  if (code !== 0) {
    console.log(`\n❌ App exited with code ${code}`);
  }
  process.exit(code);
});
