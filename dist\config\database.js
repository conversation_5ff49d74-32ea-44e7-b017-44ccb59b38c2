"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.disconnectDatabase = exports.connectDatabase = exports.prisma = void 0;
const client_1 = require("@prisma/client");
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
exports.prisma = new client_1.PrismaClient({
    log: [
        {
            emit: 'event',
            level: 'query',
        },
        {
            emit: 'event',
            level: 'error',
        },
        {
            emit: 'event',
            level: 'info',
        },
        {
            emit: 'event',
            level: 'warn',
        },
    ],
});
if (environment_1.config.nodeEnv === 'development') {
    exports.prisma.$on('query', (e) => {
        logger_1.logger.debug('Database Query', {
            query: e.query,
            params: e.params,
            duration: `${e.duration}ms`,
        });
    });
}
exports.prisma.$on('error', (e) => {
    logger_1.logger.error('Database Error', {
        target: e.target,
        message: e.message,
    });
});
const connectDatabase = async () => {
    try {
        await exports.prisma.$connect();
        logger_1.logger.info('Database connected successfully');
    }
    catch (error) {
        logger_1.logger.error('Failed to connect to database:', error);
        throw error;
    }
};
exports.connectDatabase = connectDatabase;
const disconnectDatabase = async () => {
    try {
        await exports.prisma.$disconnect();
        logger_1.logger.info('Database disconnected successfully');
    }
    catch (error) {
        logger_1.logger.error('Failed to disconnect from database:', error);
        throw error;
    }
};
exports.disconnectDatabase = disconnectDatabase;
exports.default = exports.prisma;
//# sourceMappingURL=database.js.map