{"name": "university-schedule-assistant", "version": "1.0.0", "description": "Comprehensive university academic scheduling system with constraint-based optimization", "main": "dist/server.js", "scripts": {"go": "node ultimate-start.js", "start-simple": "node simple-start.js", "dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js"}, "keywords": ["university", "scheduling", "timetable", "academic", "constraint-satisfaction", "optimization"], "author": "University Schedule Assistant Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.0", "bcryptjs": "^2.4.3", "bull": "^4.12.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "redis": "^4.6.11", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "zod": "^3.25.76"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.14", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "prisma": "^5.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/university/schedule-assistant.git"}, "bugs": {"url": "https://github.com/university/schedule-assistant/issues"}, "homepage": "https://github.com/university/schedule-assistant#readme"}