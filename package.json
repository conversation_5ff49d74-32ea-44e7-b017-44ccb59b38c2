{"name": "university-schedule-assistant", "version": "1.0.0", "description": "Comprehensive university academic scheduling system with constraint-based optimization", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:prod": "docker-compose -f docker-compose.prod.yml up"}, "keywords": ["university", "scheduling", "timetable", "academic", "constraint-satisfaction", "optimization"], "author": "University Schedule Assistant Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "exceljs": "^4.4.0", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "redis": "^4.6.11", "ioredis": "^5.3.2", "bull": "^4.12.0", "nodemailer": "^6.9.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "joi": "^17.11.0", "dotenv": "^16.3.1", "moment": "^2.29.4", "lodash": "^4.17.21"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/node": "^20.10.4", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/joi": "^17.2.3", "@types/lodash": "^4.14.202", "@types/nodemailer": "^6.4.14", "typescript": "^5.3.3", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0", "prisma": "^5.7.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/university/schedule-assistant.git"}, "bugs": {"url": "https://github.com/university/schedule-assistant/issues"}, "homepage": "https://github.com/university/schedule-assistant#readme"}