"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cacheService = exports.disconnectRedis = exports.connectRedis = exports.redis = void 0;
const ioredis_1 = __importDefault(require("ioredis"));
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
exports.redis = new ioredis_1.default(environment_1.config.redis.url, {
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: null,
    lazyConnect: true,
});
exports.redis.on('connect', () => {
    logger_1.logger.info('Redis connected successfully');
});
exports.redis.on('ready', () => {
    logger_1.logger.info('Redis ready to accept commands');
});
exports.redis.on('error', (error) => {
    logger_1.logger.error('Redis connection error:', error);
});
exports.redis.on('close', () => {
    logger_1.logger.warn('Redis connection closed');
});
exports.redis.on('reconnecting', () => {
    logger_1.logger.info('Redis reconnecting...');
});
const connectRedis = async () => {
    try {
        await exports.redis.connect();
        logger_1.logger.info('Redis connection established');
    }
    catch (error) {
        logger_1.logger.error('Failed to connect to Redis:', error);
        throw error;
    }
};
exports.connectRedis = connectRedis;
const disconnectRedis = async () => {
    try {
        await exports.redis.disconnect();
        logger_1.logger.info('Redis disconnected successfully');
    }
    catch (error) {
        logger_1.logger.error('Failed to disconnect from Redis:', error);
        throw error;
    }
};
exports.disconnectRedis = disconnectRedis;
exports.cacheService = {
    async get(key) {
        try {
            const value = await exports.redis.get(key);
            return value ? JSON.parse(value) : null;
        }
        catch (error) {
            logger_1.logger.error('Cache get error:', error);
            return null;
        }
    },
    async set(key, value, ttlSeconds) {
        try {
            const serialized = JSON.stringify(value);
            if (ttlSeconds) {
                await exports.redis.setex(key, ttlSeconds, serialized);
            }
            else {
                await exports.redis.set(key, serialized);
            }
            return true;
        }
        catch (error) {
            logger_1.logger.error('Cache set error:', error);
            return false;
        }
    },
    async del(key) {
        try {
            await exports.redis.del(key);
            return true;
        }
        catch (error) {
            logger_1.logger.error('Cache delete error:', error);
            return false;
        }
    },
    async exists(key) {
        try {
            const result = await exports.redis.exists(key);
            return result === 1;
        }
        catch (error) {
            logger_1.logger.error('Cache exists error:', error);
            return false;
        }
    },
    async expire(key, ttlSeconds) {
        try {
            await exports.redis.expire(key, ttlSeconds);
            return true;
        }
        catch (error) {
            logger_1.logger.error('Cache expire error:', error);
            return false;
        }
    },
    async mget(keys) {
        try {
            const values = await exports.redis.mget(...keys);
            return values.map(value => value ? JSON.parse(value) : null);
        }
        catch (error) {
            logger_1.logger.error('Cache mget error:', error);
            return keys.map(() => null);
        }
    },
    async mset(keyValuePairs) {
        try {
            const serializedPairs = [];
            Object.entries(keyValuePairs).forEach(([key, value]) => {
                serializedPairs.push(key, JSON.stringify(value));
            });
            await exports.redis.mset(...serializedPairs);
            return true;
        }
        catch (error) {
            logger_1.logger.error('Cache mset error:', error);
            return false;
        }
    },
    async incr(key) {
        try {
            return await exports.redis.incr(key);
        }
        catch (error) {
            logger_1.logger.error('Cache incr error:', error);
            return 0;
        }
    },
    async sadd(key, ...members) {
        try {
            return await exports.redis.sadd(key, ...members);
        }
        catch (error) {
            logger_1.logger.error('Cache sadd error:', error);
            return 0;
        }
    },
    async smembers(key) {
        try {
            return await exports.redis.smembers(key);
        }
        catch (error) {
            logger_1.logger.error('Cache smembers error:', error);
            return [];
        }
    },
    async srem(key, ...members) {
        try {
            return await exports.redis.srem(key, ...members);
        }
        catch (error) {
            logger_1.logger.error('Cache srem error:', error);
            return 0;
        }
    },
};
exports.default = exports.redis;
//# sourceMappingURL=redis.js.map