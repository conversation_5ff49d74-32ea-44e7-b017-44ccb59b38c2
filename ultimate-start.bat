@echo off
echo 🚀 University Schedule Assistant - Ultimate Start
echo.

REM Stop ALL Docker containers
echo 🛑 Stopping all Docker containers...
for /f "tokens=*" %%i in ('docker ps -aq 2^>nul') do docker stop %%i 2>nul

REM Remove ALL Docker containers
echo 🗑️ Removing all Docker containers...
for /f "tokens=*" %%i in ('docker ps -aq 2^>nul') do docker rm %%i 2>nul

echo ✅ All containers cleaned up!
echo.

REM Start fresh PostgreSQL
echo 🐘 Starting PostgreSQL...
docker run -d --name fresh_postgres -e POSTGRES_DB=schedule_assistant -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=postgres -p 5432:5432 postgres:15-alpine

REM Start fresh Redis
echo 🔴 Starting Redis...
docker run -d --name fresh_redis -p 6379:6379 redis:7-alpine

echo ⏳ Waiting for database to be ready...
timeout /t 15 /nobreak >nul

REM Test database connection
echo 🔍 Testing database connection...
docker exec fresh_postgres pg_isready -U postgres -d schedule_assistant
if %errorlevel% equ 0 (
    echo ✅ Database is ready!
) else (
    echo ❌ Database not ready, waiting more...
    timeout /t 10 /nobreak >nul
)

REM Setup database
echo 🔧 Setting up database...
npx prisma generate
npx prisma migrate dev --name fresh_start

REM Start the application with relaxed TypeScript
echo 🚀 Starting application...
echo.
echo 📍 API: http://localhost:3000
echo 📖 Docs: http://localhost:3000/api-docs
echo ❤️ Health: http://localhost:3000/health
echo.
echo Press Ctrl+C to stop
echo.

npx nodemon --exec "npx ts-node --transpile-only src/server.ts"

pause
