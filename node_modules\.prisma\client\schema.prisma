// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum RoomType {
  LECTURE_HALL
  CLASSROOM
  LAB
  TUTORIAL_ROOM
  COMPUTER_LAB
}

enum GroupType {
  FULL_LEVEL
  GROUP
  SUB_GROUP
}

enum LessonType {
  LECTURE
  LAB
  TUTORIAL
  COMPUTER_LAB
  SEMINAR
  WORKSHOP
}

enum DayOfWeek {
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
  SUNDAY
}

enum ConstraintType {
  HARD
  SOFT
}

enum RuleType {
  CONTIGUITY
  TIME_PLACEMENT
  RESOURCE_USAGE
  SPECIAL_COMBINATION
}

enum SubstitutionStatus {
  PENDING
  APPROVED
  REJECTED
  COMPLETED
}

enum SchedulingSessionStatus {
  DRAFT
  MANIFEST_GENERATED
  IN_PROGRESS
  COMPLETED
  PUBLISHED
}

// Core entities
model Semester {
  id        String   @id @default(cuid())
  name      String
  startDate DateTime @map("start_date")
  endDate   DateTime @map("end_date")
  isActive  Boolean  @default(false) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  studentLevels      StudentLevel[]
  lessons            Lesson[]
  scheduledLessons   ScheduledLesson[]
  schedulingRules    SchedulingRule[]
  schedulingSessions SchedulingSession[]

  @@map("semesters")
}

model Department {
  id               String   @id @default(cuid())
  name             String   @unique
  code             String?  @unique
  headInstructorId String?  @map("head_instructor_id")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // Relations
  headInstructor Instructor?    @relation("DepartmentHead", fields: [headInstructorId], references: [id])
  instructors    Instructor[]   @relation("DepartmentInstructors")
  rooms          Room[]
  studentLevels  StudentLevel[]
  courses        Course[]

  @@map("departments")
}

model Room {
  id                   String   @id @default(cuid())
  roomNumber           String   @unique @map("room_number")
  maxCapacity          Int      @map("max_capacity")
  roomType             RoomType @map("room_type")
  assignedDepartmentId String?  @map("assigned_department_id")
  building             String?
  floorNumber          Int?     @map("floor_number")
  equipmentNotes       String?  @map("equipment_notes")
  isActive             Boolean  @default(true) @map("is_active")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")

  // Relations
  assignedDepartment Department?       @relation(fields: [assignedDepartmentId], references: [id])
  scheduledLessons   ScheduledLesson[]

  @@map("rooms")
}

model Instructor {
  id             String   @id @default(cuid())
  name           String
  email          String?  @unique
  departmentId   String   @map("department_id")
  availability   Json     @default("{\"Monday\":[],\"Tuesday\":[],\"Wednesday\":[],\"Thursday\":[],\"Friday\":[],\"Saturday\":[],\"Sunday\":[]}")
  maxDailySlots  Int      @default(20) @map("max_daily_slots")
  preferredTimes Json     @default("{}") @map("preferred_times")
  isActive       Boolean  @default(true) @map("is_active")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  department             Department            @relation("DepartmentInstructors", fields: [departmentId], references: [id])
  headOfDepartments      Department[]          @relation("DepartmentHead")
  lessons                Lesson[]
  substitutionRequests   SubstitutionRequest[] @relation("AbsentInstructor")
  substituteRequests     SubstitutionRequest[] @relation("SubstituteInstructor")
  requestedSubstitutions SubstitutionRequest[] @relation("RequestedBy")
  approvedSubstitutions  SubstitutionRequest[] @relation("ApprovedBy")

  @@map("instructors")
}

model StudentLevel {
  id                   String   @id @default(cuid())
  name                 String
  departmentId         String   @map("department_id")
  expectedStudentCount Int      @map("expected_student_count")
  academicYear         Int      @map("academic_year")
  semesterId           String   @map("semester_id")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")

  // Relations
  department    Department     @relation(fields: [departmentId], references: [id])
  semester      Semester       @relation(fields: [semesterId], references: [id])
  studentGroups StudentGroup[]

  @@unique([name, departmentId, semesterId])
  @@map("student_levels")
}

model StudentGroup {
  id            String    @id @default(cuid())
  levelId       String    @map("level_id")
  groupName     String    @map("group_name")
  parentGroupId String?   @map("parent_group_id")
  groupType     GroupType @map("group_type")
  studentCount  Int       @map("student_count")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  // Relations
  level       StudentLevel   @relation(fields: [levelId], references: [id])
  parentGroup StudentGroup?  @relation("GroupHierarchy", fields: [parentGroupId], references: [id])
  subGroups   StudentGroup[] @relation("GroupHierarchy")
  lessons     Lesson[]

  @@unique([levelId, groupName])
  @@map("student_groups")
}

model Course {
  id           String   @id @default(cuid())
  courseCode   String   @map("course_code")
  title        String
  departmentId String   @map("department_id")
  credits      Int      @default(3)
  description  String?
  isActive     Boolean  @default(true) @map("is_active")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  department Department @relation(fields: [departmentId], references: [id])
  lessons    Lesson[]

  @@unique([courseCode, departmentId])
  @@map("courses")
}

model Lesson {
  id               String     @id @default(cuid())
  courseId         String     @map("course_id")
  studentGroupId   String     @map("student_group_id")
  instructorId     String     @map("instructor_id")
  durationInSlots  Int        @map("duration_in_slots")
  lessonType       LessonType @map("lesson_type")
  requiredRoomType RoomType   @map("required_room_type")
  semesterId       String     @map("semester_id")
  weeklyFrequency  Int        @default(1) @map("weekly_frequency")
  totalSessions    Int        @default(15) @map("total_sessions")
  isScheduled      Boolean    @default(false) @map("is_scheduled")
  createdAt        DateTime   @default(now()) @map("created_at")
  updatedAt        DateTime   @updatedAt @map("updated_at")

  // Relations
  course           Course            @relation(fields: [courseId], references: [id])
  studentGroup     StudentGroup      @relation(fields: [studentGroupId], references: [id])
  instructor       Instructor        @relation(fields: [instructorId], references: [id])
  semester         Semester          @relation(fields: [semesterId], references: [id])
  scheduledLessons ScheduledLesson[]

  @@map("lessons")
}

model TimeSlot {
  slotNumber   Int    @id @map("slot_number")
  startTime    String @map("start_time") // Using String for TIME type
  endTime      String @map("end_time") // Using String for TIME type
  displayLabel String @map("display_label")

  // Relations
  scheduledLessonsStart ScheduledLesson[] @relation("StartSlot")
  scheduledLessonsEnd   ScheduledLesson[] @relation("EndSlot")

  @@map("time_slots")
}

model ScheduledLesson {
  id         String    @id @default(cuid())
  lessonId   String    @map("lesson_id")
  roomId     String    @map("room_id")
  dayOfWeek  DayOfWeek @map("day_of_week")
  startSlot  Int       @map("start_slot")
  endSlot    Int       @map("end_slot")
  weekNumber Int       @map("week_number")
  semesterId String    @map("semester_id")
  isLocked   Boolean   @default(false) @map("is_locked")
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")

  // Relations
  lesson               Lesson                @relation(fields: [lessonId], references: [id])
  room                 Room                  @relation(fields: [roomId], references: [id])
  semester             Semester              @relation(fields: [semesterId], references: [id])
  startTimeSlot        TimeSlot              @relation("StartSlot", fields: [startSlot], references: [slotNumber])
  endTimeSlot          TimeSlot              @relation("EndSlot", fields: [endSlot], references: [slotNumber])
  substitutionRequests SubstitutionRequest[]

  @@map("scheduled_lessons")
}

// Constraints and Rules
model SchedulingRule {
  id             String         @id @default(cuid())
  ruleName       String         @map("rule_name")
  ruleType       RuleType       @map("rule_type")
  constraintType ConstraintType @map("constraint_type")
  ruleDefinition Json           @map("rule_definition")
  priority       Int            @default(1)
  isActive       Boolean        @default(true) @map("is_active")
  semesterId     String?        @map("semester_id")
  createdAt      DateTime       @default(now()) @map("created_at")
  updatedAt      DateTime       @updatedAt @map("updated_at")

  // Relations
  semester Semester? @relation(fields: [semesterId], references: [id])

  @@map("scheduling_rules")
}

// Substitution Management
model SubstitutionRequest {
  id                        String             @id @default(cuid())
  originalScheduledLessonId String             @map("original_scheduled_lesson_id")
  absentInstructorId        String             @map("absent_instructor_id")
  substituteInstructorId    String?            @map("substitute_instructor_id")
  substitutionDate          DateTime           @map("substitution_date")
  reason                    String?
  status                    SubstitutionStatus @default(PENDING)
  requestedById             String             @map("requested_by_id")
  approvedById              String?            @map("approved_by_id")
  notes                     String?
  createdAt                 DateTime           @default(now()) @map("created_at")
  updatedAt                 DateTime           @updatedAt @map("updated_at")

  // Relations
  originalScheduledLesson ScheduledLesson @relation(fields: [originalScheduledLessonId], references: [id])
  absentInstructor        Instructor      @relation("AbsentInstructor", fields: [absentInstructorId], references: [id])
  substituteInstructor    Instructor?     @relation("SubstituteInstructor", fields: [substituteInstructorId], references: [id])
  requestedBy             Instructor      @relation("RequestedBy", fields: [requestedById], references: [id])
  approvedBy              Instructor?     @relation("ApprovedBy", fields: [approvedById], references: [id])

  @@map("substitution_requests")
}

// Workflow Management
model SchedulingSession {
  id                    String                  @id @default(cuid())
  semesterId            String                  @map("semester_id")
  sessionName           String                  @map("session_name")
  status                SchedulingSessionStatus @default(DRAFT)
  manifestFilePath      String?                 @map("manifest_file_path")
  generationStartedAt   DateTime?               @map("generation_started_at")
  generationCompletedAt DateTime?               @map("generation_completed_at")
  totalLessons          Int                     @default(0) @map("total_lessons")
  scheduledLessons      Int                     @default(0) @map("scheduled_lessons")
  unscheduledLessons    Int                     @default(0) @map("unscheduled_lessons")
  createdBy             String?                 @map("created_by")
  createdAt             DateTime                @default(now()) @map("created_at")
  updatedAt             DateTime                @updatedAt @map("updated_at")

  // Relations
  semester Semester @relation(fields: [semesterId], references: [id])

  @@map("scheduling_sessions")
}
