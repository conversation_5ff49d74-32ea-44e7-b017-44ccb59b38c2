import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Seed time slots (26 slots from 9:30 AM to 4:00 PM)
  console.log('📅 Seeding time slots...');
  const timeSlots = [
    { slotNumber: 1, startTime: '09:30:00', endTime: '09:45:00', displayLabel: '9:30-9:45' },
    { slotNumber: 2, startTime: '09:45:00', endTime: '10:00:00', displayLabel: '9:45-10:00' },
    { slotNumber: 3, startTime: '10:00:00', endTime: '10:15:00', displayLabel: '10:00-10:15' },
    { slotNumber: 4, startTime: '10:15:00', endTime: '10:30:00', displayLabel: '10:15-10:30' },
    { slotNumber: 5, startTime: '10:30:00', endTime: '10:45:00', displayLabel: '10:30-10:45' },
    { slotNumber: 6, startTime: '10:45:00', endTime: '11:00:00', displayLabel: '10:45-11:00' },
    { slotNumber: 7, startTime: '11:00:00', endTime: '11:15:00', displayLabel: '11:00-11:15' },
    { slotNumber: 8, startTime: '11:15:00', endTime: '11:30:00', displayLabel: '11:15-11:30' },
    { slotNumber: 9, startTime: '11:30:00', endTime: '11:45:00', displayLabel: '11:30-11:45' },
    { slotNumber: 10, startTime: '11:45:00', endTime: '12:00:00', displayLabel: '11:45-12:00' },
    { slotNumber: 11, startTime: '12:00:00', endTime: '12:15:00', displayLabel: '12:00-12:15' },
    { slotNumber: 12, startTime: '12:15:00', endTime: '12:30:00', displayLabel: '12:15-12:30' },
    { slotNumber: 13, startTime: '12:30:00', endTime: '12:45:00', displayLabel: '12:30-12:45' },
    { slotNumber: 14, startTime: '12:45:00', endTime: '13:00:00', displayLabel: '12:45-1:00' },
    { slotNumber: 15, startTime: '13:00:00', endTime: '13:15:00', displayLabel: '1:00-1:15' },
    { slotNumber: 16, startTime: '13:15:00', endTime: '13:30:00', displayLabel: '1:15-1:30' },
    { slotNumber: 17, startTime: '13:30:00', endTime: '13:45:00', displayLabel: '1:30-1:45' },
    { slotNumber: 18, startTime: '13:45:00', endTime: '14:00:00', displayLabel: '1:45-2:00' },
    { slotNumber: 19, startTime: '14:00:00', endTime: '14:15:00', displayLabel: '2:00-2:15' },
    { slotNumber: 20, startTime: '14:15:00', endTime: '14:30:00', displayLabel: '2:15-2:30' },
    { slotNumber: 21, startTime: '14:30:00', endTime: '14:45:00', displayLabel: '2:30-2:45' },
    { slotNumber: 22, startTime: '14:45:00', endTime: '15:00:00', displayLabel: '2:45-3:00' },
    { slotNumber: 23, startTime: '15:00:00', endTime: '15:15:00', displayLabel: '3:00-3:15' },
    { slotNumber: 24, startTime: '15:15:00', endTime: '15:30:00', displayLabel: '3:15-3:30' },
    { slotNumber: 25, startTime: '15:30:00', endTime: '15:45:00', displayLabel: '3:30-3:45' },
    { slotNumber: 26, startTime: '15:45:00', endTime: '16:00:00', displayLabel: '3:45-4:00' },
  ];

  for (const slot of timeSlots) {
    await prisma.timeSlot.upsert({
      where: { slotNumber: slot.slotNumber },
      update: {},
      create: slot,
    });
  }

  // Seed sample semester
  console.log('📚 Seeding sample semester...');
  const semester = await prisma.semester.upsert({
    where: { id: 'sample-semester-id' },
    update: {},
    create: {
      id: 'sample-semester-id',
      name: 'Fall 2024',
      startDate: new Date('2024-09-01'),
      endDate: new Date('2024-12-15'),
      isActive: true,
    },
  });

  // Seed sample departments
  console.log('🏢 Seeding sample departments...');
  const departments = [
    { id: 'dept-pharmacy', name: 'Pharmacy', code: 'PHAR' },
    { id: 'dept-engineering', name: 'Engineering', code: 'ENG' },
    { id: 'dept-medicine', name: 'Medicine', code: 'MED' },
    { id: 'dept-business', name: 'Business Administration', code: 'BUS' },
  ];

  const createdDepartments = [];
  for (const dept of departments) {
    const department = await prisma.department.upsert({
      where: { id: dept.id },
      update: {},
      create: dept,
    });
    createdDepartments.push(department);
  }

  // Seed sample rooms
  console.log('🏛️ Seeding sample rooms...');
  const rooms = [
    { roomNumber: 'A101', maxCapacity: 50, roomType: 'LECTURE_HALL', building: 'Building A', floorNumber: 1 },
    { roomNumber: 'A102', maxCapacity: 30, roomType: 'CLASSROOM', building: 'Building A', floorNumber: 1 },
    { roomNumber: 'A201', maxCapacity: 25, roomType: 'LAB', building: 'Building A', floorNumber: 2, assignedDepartmentId: 'dept-pharmacy' },
    { roomNumber: 'B101', maxCapacity: 100, roomType: 'LECTURE_HALL', building: 'Building B', floorNumber: 1 },
    { roomNumber: 'B201', maxCapacity: 20, roomType: 'COMPUTER_LAB', building: 'Building B', floorNumber: 2, assignedDepartmentId: 'dept-engineering' },
    { roomNumber: 'C101', maxCapacity: 40, roomType: 'TUTORIAL_ROOM', building: 'Building C', floorNumber: 1 },
  ];

  for (const room of rooms) {
    await prisma.room.upsert({
      where: { roomNumber: room.roomNumber },
      update: {},
      create: room,
    });
  }

  // Seed sample instructors
  console.log('👨‍🏫 Seeding sample instructors...');
  const instructors = [
    {
      id: 'inst-john-doe',
      name: 'Dr. John Doe',
      email: '<EMAIL>',
      departmentId: 'dept-pharmacy',
      availability: {
        Monday: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15, 16, 17, 18, 19, 20],
        Tuesday: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15, 16, 17, 18, 19, 20],
        Wednesday: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15, 16, 17, 18, 19, 20],
        Thursday: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15, 16, 17, 18, 19, 20],
        Friday: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
        Saturday: [],
        Sunday: [],
      },
    },
    {
      id: 'inst-jane-smith',
      name: 'Prof. Jane Smith',
      email: '<EMAIL>',
      departmentId: 'dept-engineering',
      availability: {
        Monday: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 17, 18, 19, 20, 21, 22],
        Tuesday: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 17, 18, 19, 20, 21, 22],
        Wednesday: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 17, 18, 19, 20, 21, 22],
        Thursday: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 17, 18, 19, 20, 21, 22],
        Friday: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        Saturday: [],
        Sunday: [],
      },
    },
    {
      id: 'inst-mike-wilson',
      name: 'Dr. Mike Wilson',
      email: '<EMAIL>',
      departmentId: 'dept-medicine',
      availability: {
        Monday: [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 19, 20, 21, 22, 23, 24],
        Tuesday: [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 19, 20, 21, 22, 23, 24],
        Wednesday: [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 19, 20, 21, 22, 23, 24],
        Thursday: [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 19, 20, 21, 22, 23, 24],
        Friday: [5, 6, 7, 8, 9, 10, 11, 12, 13, 14],
        Saturday: [],
        Sunday: [],
      },
    },
  ];

  for (const instructor of instructors) {
    await prisma.instructor.upsert({
      where: { id: instructor.id },
      update: {},
      create: instructor,
    });
  }

  // Seed sample student levels
  console.log('🎓 Seeding sample student levels...');
  const studentLevels = [
    {
      id: 'level-pharmacy-1',
      name: 'First Year Pharmacy',
      departmentId: 'dept-pharmacy',
      expectedStudentCount: 120,
      academicYear: 1,
      semesterId: semester.id,
    },
    {
      id: 'level-engineering-1',
      name: 'First Year Engineering',
      departmentId: 'dept-engineering',
      expectedStudentCount: 150,
      academicYear: 1,
      semesterId: semester.id,
    },
    {
      id: 'level-medicine-2',
      name: 'Second Year Medicine',
      departmentId: 'dept-medicine',
      expectedStudentCount: 80,
      academicYear: 2,
      semesterId: semester.id,
    },
  ];

  for (const level of studentLevels) {
    await prisma.studentLevel.upsert({
      where: { id: level.id },
      update: {},
      create: level,
    });
  }

  // Seed sample student groups
  console.log('👥 Seeding sample student groups...');
  const studentGroups = [
    {
      id: 'group-pharmacy-1-full',
      levelId: 'level-pharmacy-1',
      groupName: 'Full Level',
      groupType: 'FULL_LEVEL',
      studentCount: 120,
    },
    {
      id: 'group-pharmacy-1-a',
      levelId: 'level-pharmacy-1',
      groupName: 'Group A',
      parentGroupId: 'group-pharmacy-1-full',
      groupType: 'GROUP',
      studentCount: 60,
    },
    {
      id: 'group-pharmacy-1-b',
      levelId: 'level-pharmacy-1',
      groupName: 'Group B',
      parentGroupId: 'group-pharmacy-1-full',
      groupType: 'GROUP',
      studentCount: 60,
    },
    {
      id: 'group-pharmacy-1-a1',
      levelId: 'level-pharmacy-1',
      groupName: 'Sub-Group A1',
      parentGroupId: 'group-pharmacy-1-a',
      groupType: 'SUB_GROUP',
      studentCount: 30,
    },
    {
      id: 'group-pharmacy-1-a2',
      levelId: 'level-pharmacy-1',
      groupName: 'Sub-Group A2',
      parentGroupId: 'group-pharmacy-1-a',
      groupType: 'SUB_GROUP',
      studentCount: 30,
    },
  ];

  for (const group of studentGroups) {
    await prisma.studentGroup.upsert({
      where: { id: group.id },
      update: {},
      create: group,
    });
  }

  // Seed sample courses
  console.log('📖 Seeding sample courses...');
  const courses = [
    {
      id: 'course-phar-101',
      courseCode: 'PHAR101',
      title: 'Introduction to Pharmacy',
      departmentId: 'dept-pharmacy',
      credits: 3,
      description: 'Basic principles of pharmaceutical sciences',
    },
    {
      id: 'course-phar-102',
      courseCode: 'PHAR102',
      title: 'Pharmaceutical Chemistry Lab',
      departmentId: 'dept-pharmacy',
      credits: 2,
      description: 'Laboratory work in pharmaceutical chemistry',
    },
    {
      id: 'course-eng-101',
      courseCode: 'ENG101',
      title: 'Engineering Mathematics',
      departmentId: 'dept-engineering',
      credits: 4,
      description: 'Mathematical foundations for engineering',
    },
  ];

  for (const course of courses) {
    await prisma.course.upsert({
      where: { id: course.id },
      update: {},
      create: course,
    });
  }

  console.log('✅ Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
