import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Simple environment validation without zod
function getEnvVar(name: string, defaultValue?: string): string {
  const value = process.env[name];
  if (!value && !defaultValue) {
    throw new Error(`Environment variable ${name} is required`);
  }
  return value || defaultValue!;
}

function getEnvNumber(name: string, defaultValue: number): number {
  const value = process.env[name];
  return value ? parseInt(value, 10) : defaultValue;
}

function getEnvBoolean(name: string, defaultValue: boolean): boolean {
  const value = process.env[name];
  return value ? value.toLowerCase() === 'true' : defaultValue;
}

// Simple environment configuration
const env = {
  NODE_ENV: getEnvVar('NODE_ENV', 'development') as 'development' | 'production' | 'test',
  PORT: getEnvNumber('PORT', 3000),
  API_VERSION: getEnvVar('API_VERSION', 'v1'),

  DATABASE_URL: getEnvVar('DATABASE_URL'),
  DATABASE_HOST: getEnvVar('DATABASE_HOST', 'localhost'),
  DATABASE_PORT: getEnvNumber('DATABASE_PORT', 5432),
  DATABASE_NAME: getEnvVar('DATABASE_NAME', 'schedule_assistant'),
  DATABASE_USER: getEnvVar('DATABASE_USER', 'postgres'),
  DATABASE_PASSWORD: getEnvVar('DATABASE_PASSWORD', 'postgres'),

  REDIS_URL: getEnvVar('REDIS_URL', 'redis://localhost:6379'),
  REDIS_HOST: getEnvVar('REDIS_HOST', 'localhost'),
  REDIS_PORT: getEnvNumber('REDIS_PORT', 6379),
  REDIS_PASSWORD: process.env.REDIS_PASSWORD,

  JWT_SECRET: getEnvVar('JWT_SECRET'),
  JWT_EXPIRES_IN: getEnvVar('JWT_EXPIRES_IN', '24h'),
  JWT_REFRESH_SECRET: getEnvVar('JWT_REFRESH_SECRET'),
  JWT_REFRESH_EXPIRES_IN: getEnvVar('JWT_REFRESH_EXPIRES_IN', '7d'),

  CORS_ORIGIN: getEnvVar('CORS_ORIGIN', 'http://localhost:3000'),
  CORS_CREDENTIALS: getEnvBoolean('CORS_CREDENTIALS', true),

  RATE_LIMIT_WINDOW_MS: getEnvNumber('RATE_LIMIT_WINDOW_MS', 900000),
  RATE_LIMIT_MAX_REQUESTS: getEnvNumber('RATE_LIMIT_MAX_REQUESTS', 100),

  MAX_FILE_SIZE: getEnvNumber('MAX_FILE_SIZE', 10485760),
  UPLOAD_PATH: getEnvVar('UPLOAD_PATH', './uploads'),
  ALLOWED_FILE_TYPES: getEnvVar('ALLOWED_FILE_TYPES', '.xlsx,.xls,.csv'),

  LOG_LEVEL: getEnvVar('LOG_LEVEL', 'info') as 'error' | 'warn' | 'info' | 'debug',
  LOG_FILE_PATH: getEnvVar('LOG_FILE_PATH', './logs'),
  LOG_MAX_SIZE: getEnvVar('LOG_MAX_SIZE', '20m'),
  LOG_MAX_FILES: getEnvVar('LOG_MAX_FILES', '14d'),

  BCRYPT_ROUNDS: getEnvNumber('BCRYPT_ROUNDS', 12),
  SESSION_SECRET: getEnvVar('SESSION_SECRET'),

  SWAGGER_ENABLED: getEnvBoolean('SWAGGER_ENABLED', true),
  SWAGGER_PATH: getEnvVar('SWAGGER_PATH', '/api-docs'),
};

// Export configuration object
export const config = {
  nodeEnv: env.NODE_ENV,
  port: env.PORT,
  apiVersion: env.API_VERSION,

  database: {
    url: env.DATABASE_URL,
    host: env.DATABASE_HOST,
    port: env.DATABASE_PORT,
    name: env.DATABASE_NAME,
    user: env.DATABASE_USER,
    password: env.DATABASE_PASSWORD,
  },

  redis: {
    url: env.REDIS_URL,
    host: env.REDIS_HOST,
    port: env.REDIS_PORT,
    password: env.REDIS_PASSWORD,
  },

  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: env.JWT_EXPIRES_IN,
    refreshSecret: env.JWT_REFRESH_SECRET,
    refreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN,
  },

  cors: {
    origin: env.CORS_ORIGIN.split(',').map((origin: string) => origin.trim()),
    credentials: env.CORS_CREDENTIALS,
  },

  rateLimit: {
    windowMs: env.RATE_LIMIT_WINDOW_MS,
    maxRequests: env.RATE_LIMIT_MAX_REQUESTS,
  },

  upload: {
    maxFileSize: env.MAX_FILE_SIZE,
    uploadPath: env.UPLOAD_PATH,
    allowedFileTypes: env.ALLOWED_FILE_TYPES.split(',').map((type: string) => type.trim()),
  },

  email: {
    host: env.SMTP_HOST,
    port: env.SMTP_PORT,
    secure: env.SMTP_SECURE,
    user: env.SMTP_USER,
    password: env.SMTP_PASSWORD,
    from: env.EMAIL_FROM,
  },

  logging: {
    level: env.LOG_LEVEL,
    filePath: env.LOG_FILE_PATH,
    maxSize: env.LOG_MAX_SIZE,
    maxFiles: env.LOG_MAX_FILES,
  },

  scheduling: {
    timeoutMs: env.SCHEDULING_TIMEOUT_MS,
    maxIterations: env.MAX_SCHEDULING_ITERATIONS,
    optimizationLevel: env.OPTIMIZATION_LEVEL,
  },

  jobs: {
    redisUrl: env.BULL_REDIS_URL,
    concurrency: env.JOB_CONCURRENCY,
  },

  security: {
    bcryptRounds: env.BCRYPT_ROUNDS,
    sessionSecret: env.SESSION_SECRET,
  },

  swagger: {
    enabled: env.SWAGGER_ENABLED,
    path: env.SWAGGER_PATH,
  },

  monitoring: {
    healthCheckEnabled: env.HEALTH_CHECK_ENABLED,
    metricsEnabled: env.METRICS_ENABLED,
  },
} as const;

export type Config = typeof config;
