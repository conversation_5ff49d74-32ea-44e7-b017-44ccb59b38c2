"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const zod_1 = require("zod");
dotenv_1.default.config();
const envSchema = zod_1.z.object({
    NODE_ENV: zod_1.z.enum(['development', 'production', 'test']).default('development'),
    PORT: zod_1.z.string().transform(Number).default('3000'),
    API_VERSION: zod_1.z.string().default('v1'),
    DATABASE_URL: zod_1.z.string(),
    DATABASE_HOST: zod_1.z.string().default('localhost'),
    DATABASE_PORT: zod_1.z.string().transform(Number).default('5432'),
    DATABASE_NAME: zod_1.z.string().default('schedule_assistant'),
    DATABASE_USER: zod_1.z.string(),
    DATABASE_PASSWORD: zod_1.z.string(),
    REDIS_URL: zod_1.z.string().default('redis://localhost:6379'),
    REDIS_HOST: zod_1.z.string().default('localhost'),
    REDIS_PORT: zod_1.z.string().transform(Number).default('6379'),
    REDIS_PASSWORD: zod_1.z.string().optional(),
    JWT_SECRET: zod_1.z.string().min(32),
    JWT_EXPIRES_IN: zod_1.z.string().default('24h'),
    JWT_REFRESH_SECRET: zod_1.z.string().min(32),
    JWT_REFRESH_EXPIRES_IN: zod_1.z.string().default('7d'),
    CORS_ORIGIN: zod_1.z.string().default('http://localhost:3000'),
    CORS_CREDENTIALS: zod_1.z.string().transform(Boolean).default('true'),
    RATE_LIMIT_WINDOW_MS: zod_1.z.string().transform(Number).default('900000'),
    RATE_LIMIT_MAX_REQUESTS: zod_1.z.string().transform(Number).default('100'),
    MAX_FILE_SIZE: zod_1.z.string().transform(Number).default('10485760'),
    UPLOAD_PATH: zod_1.z.string().default('./uploads'),
    ALLOWED_FILE_TYPES: zod_1.z.string().default('.xlsx,.xls,.csv'),
    SMTP_HOST: zod_1.z.string().optional(),
    SMTP_PORT: zod_1.z.string().transform(Number).optional(),
    SMTP_SECURE: zod_1.z.string().transform(Boolean).default('false'),
    SMTP_USER: zod_1.z.string().optional(),
    SMTP_PASSWORD: zod_1.z.string().optional(),
    EMAIL_FROM: zod_1.z.string().optional(),
    LOG_LEVEL: zod_1.z.enum(['error', 'warn', 'info', 'debug']).default('info'),
    LOG_FILE_PATH: zod_1.z.string().default('./logs'),
    LOG_MAX_SIZE: zod_1.z.string().default('20m'),
    LOG_MAX_FILES: zod_1.z.string().default('14d'),
    SCHEDULING_TIMEOUT_MS: zod_1.z.string().transform(Number).default('3600000'),
    MAX_SCHEDULING_ITERATIONS: zod_1.z.string().transform(Number).default('10000'),
    OPTIMIZATION_LEVEL: zod_1.z.enum(['low', 'medium', 'high']).default('high'),
    BULL_REDIS_URL: zod_1.z.string().default('redis://localhost:6379'),
    JOB_CONCURRENCY: zod_1.z.string().transform(Number).default('5'),
    BCRYPT_ROUNDS: zod_1.z.string().transform(Number).default('12'),
    SESSION_SECRET: zod_1.z.string().min(32),
    SWAGGER_ENABLED: zod_1.z.string().transform(Boolean).default('true'),
    SWAGGER_PATH: zod_1.z.string().default('/api-docs'),
    HEALTH_CHECK_ENABLED: zod_1.z.string().transform(Boolean).default('true'),
    METRICS_ENABLED: zod_1.z.string().transform(Boolean).default('true'),
});
const env = envSchema.parse(process.env);
exports.config = {
    nodeEnv: env.NODE_ENV,
    port: env.PORT,
    apiVersion: env.API_VERSION,
    database: {
        url: env.DATABASE_URL,
        host: env.DATABASE_HOST,
        port: env.DATABASE_PORT,
        name: env.DATABASE_NAME,
        user: env.DATABASE_USER,
        password: env.DATABASE_PASSWORD,
    },
    redis: {
        url: env.REDIS_URL,
        host: env.REDIS_HOST,
        port: env.REDIS_PORT,
        password: env.REDIS_PASSWORD,
    },
    jwt: {
        secret: env.JWT_SECRET,
        expiresIn: env.JWT_EXPIRES_IN,
        refreshSecret: env.JWT_REFRESH_SECRET,
        refreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN,
    },
    cors: {
        origin: env.CORS_ORIGIN.split(',').map(origin => origin.trim()),
        credentials: env.CORS_CREDENTIALS,
    },
    rateLimit: {
        windowMs: env.RATE_LIMIT_WINDOW_MS,
        maxRequests: env.RATE_LIMIT_MAX_REQUESTS,
    },
    upload: {
        maxFileSize: env.MAX_FILE_SIZE,
        uploadPath: env.UPLOAD_PATH,
        allowedFileTypes: env.ALLOWED_FILE_TYPES.split(',').map(type => type.trim()),
    },
    email: {
        host: env.SMTP_HOST,
        port: env.SMTP_PORT,
        secure: env.SMTP_SECURE,
        user: env.SMTP_USER,
        password: env.SMTP_PASSWORD,
        from: env.EMAIL_FROM,
    },
    logging: {
        level: env.LOG_LEVEL,
        filePath: env.LOG_FILE_PATH,
        maxSize: env.LOG_MAX_SIZE,
        maxFiles: env.LOG_MAX_FILES,
    },
    scheduling: {
        timeoutMs: env.SCHEDULING_TIMEOUT_MS,
        maxIterations: env.MAX_SCHEDULING_ITERATIONS,
        optimizationLevel: env.OPTIMIZATION_LEVEL,
    },
    jobs: {
        redisUrl: env.BULL_REDIS_URL,
        concurrency: env.JOB_CONCURRENCY,
    },
    security: {
        bcryptRounds: env.BCRYPT_ROUNDS,
        sessionSecret: env.SESSION_SECRET,
    },
    swagger: {
        enabled: env.SWAGGER_ENABLED,
        path: env.SWAGGER_PATH,
    },
    monitoring: {
        healthCheckEnabled: env.HEALTH_CHECK_ENABLED,
        metricsEnabled: env.METRICS_ENABLED,
    },
};
//# sourceMappingURL=environment.js.map