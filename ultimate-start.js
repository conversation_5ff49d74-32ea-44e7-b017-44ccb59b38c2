const { execSync, spawn } = require('child_process');

console.log('🚀 University Schedule Assistant - Ultimate Start\n');

// Stop ALL Docker containers
console.log('🛑 Stopping all Docker containers...');
try {
  const containers = execSync('docker ps -aq', { encoding: 'utf8', stdio: 'pipe' }).trim();
  if (containers) {
    execSync(`docker stop ${containers.split('\n').join(' ')}`, { stdio: 'pipe' });
  }
} catch (e) {
  // Ignore errors
}

// Remove ALL Docker containers
console.log('🗑️ Removing all Docker containers...');
try {
  const containers = execSync('docker ps -aq', { encoding: 'utf8', stdio: 'pipe' }).trim();
  if (containers) {
    execSync(`docker rm ${containers.split('\n').join(' ')}`, { stdio: 'pipe' });
  }
} catch (e) {
  // Ignore errors
}

console.log('✅ All containers cleaned up!\n');

// Start fresh PostgreSQL
console.log('🐘 Starting PostgreSQL...');
try {
  execSync('docker run -d --name fresh_postgres -e POSTGRES_DB=schedule_assistant -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=postgres -p 5432:5432 postgres:15-alpine', { stdio: 'inherit' });
  console.log('✅ PostgreSQL started');
} catch (e) {
  console.log('❌ Failed to start PostgreSQL');
  process.exit(1);
}

// Start fresh Redis
console.log('🔴 Starting Redis...');
try {
  execSync('docker run -d --name fresh_redis -p 6379:6379 redis:7-alpine', { stdio: 'inherit' });
  console.log('✅ Redis started');
} catch (e) {
  console.log('❌ Failed to start Redis');
  process.exit(1);
}

// Wait for database
console.log('⏳ Waiting for database to be ready...');
let ready = false;
for (let i = 0; i < 30; i++) {
  try {
    execSync('docker exec fresh_postgres pg_isready -U postgres -d schedule_assistant', { stdio: 'pipe' });
    ready = true;
    break;
  } catch (e) {
    process.stdout.write('.');
    // Cross-platform sleep
    execSync('node -e "setTimeout(() => {}, 2000)"', { stdio: 'pipe' });
  }
}

if (!ready) {
  console.log('\n❌ Database not ready after 60 seconds');
  process.exit(1);
}
console.log('\n✅ Database is ready!');

// Setup database
console.log('🔧 Setting up database...');
try {
  execSync('npx prisma generate', { stdio: 'inherit' });
  execSync('npx prisma migrate dev --name fresh_start', { stdio: 'inherit' });
  console.log('✅ Database setup complete');
} catch (e) {
  console.log('⚠️ Database setup had issues, but continuing...');
}

// Start the application
console.log('🚀 Starting application...');
console.log('\n🎉 Application starting!');
console.log('📍 API: http://localhost:3000');
console.log('📖 Docs: http://localhost:3000/api-docs');
console.log('❤️ Health: http://localhost:3000/health');
console.log('\nPress Ctrl+C to stop\n');

// Start with relaxed TypeScript
const app = spawn('npx', ['nodemon', '--exec', 'npx ts-node --transpile-only src/server.ts'], {
  stdio: 'inherit',
  shell: true
});

// Handle shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down...');
  app.kill();
  process.exit(0);
});

app.on('exit', (code) => {
  if (code !== 0) {
    console.log(`\n❌ App exited with code ${code}`);
  }
  process.exit(code);
});
