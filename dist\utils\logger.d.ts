import winston from 'winston';
export declare const logger: winston.Logger;
export declare const loggerStream: {
    write: (message: string) => void;
};
export declare const logError: (message: string, error?: Error, meta?: object) => void;
export declare const logInfo: (message: string, meta?: object) => void;
export declare const logWarn: (message: string, meta?: object) => void;
export declare const logDebug: (message: string, meta?: object) => void;
export declare const logPerformance: (operation: string, startTime: number, meta?: object) => void;
export declare const logQuery: (query: string, params?: any[], duration?: number) => void;
export declare const logScheduling: (operation: string, meta?: object) => void;
export declare const logUserAction: (userId: string, action: string, resource: string, meta?: object) => void;
export default logger;
//# sourceMappingURL=logger.d.ts.map