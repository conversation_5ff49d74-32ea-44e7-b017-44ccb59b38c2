# University Schedule Assistant - REST API Design

## Base URL
```
https://api.schedule-assistant.university.edu/v1
```

## Authentication
All endpoints require JWT authentication except for public read-only views.

## Response Format
All responses follow this structure:
```json
{
  "success": boolean,
  "data": object | array,
  "message": string,
  "errors": array,
  "pagination": object (when applicable)
}
```

## Error Codes
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 409: Conflict (scheduling conflicts)
- 422: Validation Error
- 500: Internal Server Error

---

## 1. CORE ENTITY MANAGEMENT APIs

### Semesters
```
GET    /semesters                    # List all semesters
POST   /semesters                    # Create new semester
GET    /semesters/{id}               # Get semester details
PUT    /semesters/{id}               # Update semester
DELETE /semesters/{id}               # Delete semester
POST   /semesters/{id}/activate      # Set as active semester
```

### Departments
```
GET    /departments                  # List all departments
POST   /departments                  # Create new department
GET    /departments/{id}             # Get department details
PUT    /departments/{id}             # Update department
DELETE /departments/{id}             # Delete department
GET    /departments/{id}/instructors # Get department instructors
GET    /departments/{id}/courses     # Get department courses
```

### Rooms
```
GET    /rooms                        # List all rooms (with filters)
POST   /rooms                        # Create new room
GET    /rooms/{id}                   # Get room details
PUT    /rooms/{id}                   # Update room
DELETE /rooms/{id}                   # Delete room
GET    /rooms/availability           # Check room availability
```

Query parameters for GET /rooms:
- `type`: Filter by room type
- `capacity_min`: Minimum capacity
- `capacity_max`: Maximum capacity
- `department_id`: Filter by assigned department
- `available_on`: Check availability on specific day/time

### Instructors
```
GET    /instructors                  # List all instructors
POST   /instructors                  # Create new instructor
GET    /instructors/{id}             # Get instructor details
PUT    /instructors/{id}             # Update instructor
DELETE /instructors/{id}             # Delete instructor
PUT    /instructors/{id}/availability # Update availability
GET    /instructors/{id}/schedule    # Get instructor's schedule
```

### Student Levels
```
GET    /student-levels               # List all student levels
POST   /student-levels               # Create new student level
GET    /student-levels/{id}          # Get student level details
PUT    /student-levels/{id}          # Update student level
DELETE /student-levels/{id}          # Delete student level
GET    /student-levels/{id}/groups   # Get groups for this level
```

### Student Groups
```
GET    /student-groups               # List all student groups
POST   /student-groups               # Create new student group
GET    /student-groups/{id}          # Get student group details
PUT    /student-groups/{id}          # Update student group
DELETE /student-groups/{id}          # Delete student group
GET    /student-groups/{id}/schedule # Get group's schedule
```

### Courses
```
GET    /courses                      # List all courses
POST   /courses                      # Create new course
GET    /courses/{id}                 # Get course details
PUT    /courses/{id}                 # Update course
DELETE /courses/{id}                 # Delete course
GET    /courses/{id}/lessons         # Get lessons for this course
```

### Lessons
```
GET    /lessons                      # List all lessons (with filters)
POST   /lessons                      # Create new lesson
GET    /lessons/{id}                 # Get lesson details
PUT    /lessons/{id}                 # Update lesson
DELETE /lessons/{id}                 # Delete lesson
POST   /lessons/bulk                 # Create multiple lessons
```

---

## 2. WORKFLOW MANAGEMENT APIs

### Data Import/Export
```
POST   /import/excel                 # Bulk import from Excel files
GET    /export/template/{entity}     # Download Excel template
POST   /export/data                  # Export current data to Excel
```

### Scheduling Manifest Generation
```
POST   /scheduling/generate-manifest # Generate scheduling manifest
GET    /scheduling/manifest/{session_id} # Download manifest file
POST   /scheduling/upload-manifest   # Upload modified manifest
GET    /scheduling/sessions          # List scheduling sessions
GET    /scheduling/sessions/{id}     # Get session details
```

### Scheduling Engine
```
POST   /scheduling/start             # Start automatic scheduling
GET    /scheduling/status/{session_id} # Get scheduling progress
POST   /scheduling/stop/{session_id} # Stop scheduling process
GET    /scheduling/conflicts         # Get scheduling conflicts
POST   /scheduling/resolve-conflict  # Resolve specific conflict
```

### Manual Schedule Management
```
GET    /schedule                     # Get current schedule (with filters)
POST   /schedule/move-lesson         # Move lesson to different slot
POST   /schedule/lock-lesson         # Lock lesson in current position
POST   /schedule/unlock-lesson       # Unlock lesson
GET    /schedule/conflicts           # Check for conflicts
POST   /schedule/validate            # Validate entire schedule
```

---

## 3. SUBSTITUTION MANAGEMENT APIs

### Substitution Requests
```
GET    /substitutions                # List substitution requests
POST   /substitutions                # Create substitution request
GET    /substitutions/{id}           # Get substitution details
PUT    /substitutions/{id}           # Update substitution
DELETE /substitutions/{id}           # Cancel substitution
POST   /substitutions/{id}/approve   # Approve substitution
POST   /substitutions/{id}/reject    # Reject substitution
```

### Daily Operations
```
GET    /substitutions/daily/{date}   # Get daily substitutions
POST   /substitutions/suggest        # Get substitute suggestions
GET    /substitutions/bulletin/{date} # Generate daily bulletin
```

---

## 4. REPORTING AND PUBLISHING APIs

### Schedule Views
```
GET    /schedules/instructor/{id}    # Instructor's schedule
GET    /schedules/room/{id}          # Room's schedule
GET    /schedules/group/{id}         # Student group's schedule
GET    /schedules/department/{id}    # Department's schedule
```

### Export and Publishing
```
GET    /export/schedule/{type}/{id}  # Export specific schedule
POST   /publish/schedule             # Publish schedule to public view
GET    /public/schedule/{type}/{id}  # Public read-only schedule view
```

### Reports
```
GET    /reports/utilization/rooms    # Room utilization report
GET    /reports/utilization/instructors # Instructor workload report
GET    /reports/conflicts            # Conflicts report
GET    /reports/statistics           # General statistics
```

---

## 5. CONSTRAINT AND RULES MANAGEMENT

### Scheduling Rules
```
GET    /rules                        # List all scheduling rules
POST   /rules                        # Create new rule
GET    /rules/{id}                   # Get rule details
PUT    /rules/{id}                   # Update rule
DELETE /rules/{id}                   # Delete rule
POST   /rules/{id}/toggle            # Enable/disable rule
```

---

## 6. SYSTEM CONFIGURATION

### Time Slots
```
GET    /time-slots                   # Get all time slots
PUT    /time-slots/config            # Update time slot configuration
```

### System Settings
```
GET    /settings                     # Get system settings
PUT    /settings                     # Update system settings
```

---

## Request/Response Examples

### Create Lesson
```http
POST /lessons
Content-Type: application/json

{
  "course_id": "uuid",
  "student_group_id": "uuid", 
  "instructor_id": "uuid",
  "duration_in_slots": 4,
  "lesson_type": "Lecture",
  "required_room_type": "Lecture Hall",
  "semester_id": "uuid",
  "weekly_frequency": 1,
  "total_sessions": 15
}
```

### Move Lesson
```http
POST /schedule/move-lesson
Content-Type: application/json

{
  "lesson_id": "uuid",
  "new_room_id": "uuid",
  "new_day": "Monday",
  "new_start_slot": 5,
  "validate_conflicts": true
}
```

### Generate Manifest
```http
POST /scheduling/generate-manifest
Content-Type: application/json

{
  "semester_id": "uuid",
  "session_name": "Fall 2024 Schedule",
  "include_locked_lessons": false
}
```
