"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.routes = void 0;
const express_1 = require("express");
const publicRouter = (0, express_1.Router)();
const protectedRouter = (0, express_1.Router)();
publicRouter.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'API is healthy',
        timestamp: new Date().toISOString(),
    });
});
exports.routes = {
    public: publicRouter,
    protected: protectedRouter,
};
//# sourceMappingURL=index.js.map