@echo off
echo ╔══════════════════════════════════════════════════════════════╗
echo ║            University Schedule Assistant                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Install from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Starting application...
npm run setup

pause
