@echo off
REM University Schedule Assistant - Windows Startup Script

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║            University Schedule Assistant                     ║
echo ║                                                              ║
echo ║     Comprehensive Academic Scheduling System                ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed or not in PATH
    echo Please install npm or use Node.js installer
    pause
    exit /b 1
)

echo ✅ Node.js and npm are available
echo.

REM Check if Docker is available
echo 🐳 Checking Docker availability...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not available
    echo Please install Docker Desktop or use manual PostgreSQL setup
    echo.
    echo Alternative: Install PostgreSQL manually from https://www.postgresql.org/download/
    pause
    exit /b 1
)

echo ✅ Docker is available
echo.

REM Clean up existing containers
echo 🧹 Cleaning up existing containers...
npm run cleanup

REM Start with quick-start script
echo 🚀 Starting University Schedule Assistant...
echo.

npm run quick-start

if %errorlevel% neq 0 (
    echo.
    echo ❌ Application failed to start
    echo Please check the error messages above
    echo.
    echo You can also try:
    echo 1. npm run cleanup
    echo 2. npm run quick-start
    pause
    exit /b 1
)

pause
