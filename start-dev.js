#!/usr/bin/env node

/**
 * Simple Development Server Starter
 * Bypasses build issues and starts the dev server directly
 */

const { spawn } = require('child_process');
const path = require('path');

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function main() {
  log('🚀 Starting University Schedule Assistant in Development Mode...', 'green');
  log('API will be available at: http://localhost:3000', 'cyan');
  log('API Documentation: http://localhost:3000/api-docs', 'cyan');
  log('Press Ctrl+C to stop\n', 'yellow');

  // Start nodemon directly
  const nodemonPath = path.join(__dirname, 'node_modules', '.bin', 'nodemon');
  const devProcess = spawn('node', [nodemonPath, 'src/server.ts'], {
    stdio: 'inherit',
    shell: true,
    cwd: __dirname
  });

  // Handle process termination
  process.on('SIGINT', () => {
    log('\n👋 Shutting down development server...', 'yellow');
    devProcess.kill('SIGINT');
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    log('\n👋 Shutting down development server...', 'yellow');
    devProcess.kill('SIGTERM');
    process.exit(0);
  });

  devProcess.on('error', (error) => {
    log(`❌ Failed to start development server: ${error.message}`, 'red');
    log('Trying alternative method...', 'yellow');
    
    // Fallback: try npx nodemon
    const fallbackProcess = spawn('npx', ['nodemon', 'src/server.ts'], {
      stdio: 'inherit',
      shell: true,
      cwd: __dirname
    });

    fallbackProcess.on('error', (fallbackError) => {
      log(`❌ Fallback also failed: ${fallbackError.message}`, 'red');
      log('Please try running manually: npx nodemon src/server.ts', 'yellow');
      process.exit(1);
    });
  });

  devProcess.on('exit', (code) => {
    if (code !== 0) {
      log(`❌ Development server exited with code ${code}`, 'red');
    }
    process.exit(code);
  });
}

main();
