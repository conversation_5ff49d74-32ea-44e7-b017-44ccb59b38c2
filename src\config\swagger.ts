import { Application } from 'express';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { config } from '@/config/environment';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'University Schedule Assistant API',
      version: '1.0.0',
      description: 'Comprehensive university academic scheduling system with constraint-based optimization',
      contact: {
        name: 'API Support',
        email: '<EMAIL>',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    servers: [
      {
        url: `http://localhost:${config.port}/api/${config.apiVersion}`,
        description: 'Development server',
      },
      {
        url: `https://api.schedule-assistant.university.edu/${config.apiVersion}`,
        description: 'Production server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false,
            },
            message: {
              type: 'string',
              example: 'Error message',
            },
            error: {
              type: 'string',
              example: 'ErrorType',
            },
            code: {
              type: 'string',
              example: 'ERROR_CODE',
            },
            timestamp: {
              type: 'string',
              format: 'date-time',
            },
            path: {
              type: 'string',
              example: '/api/v1/endpoint',
            },
            method: {
              type: 'string',
              example: 'GET',
            },
          },
        },
        ValidationError: {
          allOf: [
            { $ref: '#/components/schemas/Error' },
            {
              type: 'object',
              properties: {
                errors: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      field: {
                        type: 'string',
                        example: 'email',
                      },
                      message: {
                        type: 'string',
                        example: 'Email is required',
                      },
                      value: {
                        type: 'string',
                        example: 'invalid-email',
                      },
                    },
                  },
                },
              },
            },
          ],
        },
        SchedulingConflict: {
          allOf: [
            { $ref: '#/components/schemas/Error' },
            {
              type: 'object',
              properties: {
                conflicts: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      type: {
                        type: 'string',
                        enum: ['room', 'instructor', 'student_group'],
                      },
                      resource_id: {
                        type: 'string',
                        format: 'uuid',
                      },
                      conflicting_lessons: {
                        type: 'array',
                        items: {
                          type: 'string',
                          format: 'uuid',
                        },
                      },
                      time_slot: {
                        type: 'object',
                        properties: {
                          day: {
                            type: 'string',
                            enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
                          },
                          start_slot: {
                            type: 'integer',
                            minimum: 1,
                            maximum: 26,
                          },
                          end_slot: {
                            type: 'integer',
                            minimum: 1,
                            maximum: 26,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          ],
        },
        Pagination: {
          type: 'object',
          properties: {
            page: {
              type: 'integer',
              minimum: 1,
              example: 1,
            },
            limit: {
              type: 'integer',
              minimum: 1,
              maximum: 100,
              example: 10,
            },
            total: {
              type: 'integer',
              minimum: 0,
              example: 100,
            },
            pages: {
              type: 'integer',
              minimum: 0,
              example: 10,
            },
            hasNext: {
              type: 'boolean',
              example: true,
            },
            hasPrev: {
              type: 'boolean',
              example: false,
            },
          },
        },
        SuccessResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true,
            },
            message: {
              type: 'string',
              example: 'Operation completed successfully',
            },
            data: {
              type: 'object',
            },
            pagination: {
              $ref: '#/components/schemas/Pagination',
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
    tags: [
      {
        name: 'Authentication',
        description: 'User authentication and authorization',
      },
      {
        name: 'Semesters',
        description: 'Semester management',
      },
      {
        name: 'Departments',
        description: 'Department management',
      },
      {
        name: 'Rooms',
        description: 'Room management',
      },
      {
        name: 'Instructors',
        description: 'Instructor management',
      },
      {
        name: 'Student Levels',
        description: 'Student level management',
      },
      {
        name: 'Student Groups',
        description: 'Student group management',
      },
      {
        name: 'Courses',
        description: 'Course management',
      },
      {
        name: 'Lessons',
        description: 'Lesson management',
      },
      {
        name: 'Scheduling',
        description: 'Schedule generation and management',
      },
      {
        name: 'Substitutions',
        description: 'Substitution management',
      },
      {
        name: 'Reports',
        description: 'Reporting and analytics',
      },
    ],
  },
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts',
    './src/types/*.ts',
  ],
};

const specs = swaggerJsdoc(options);

export const setupSwagger = (app: Application): void => {
  app.use(
    config.swagger.path,
    swaggerUi.serve,
    swaggerUi.setup(specs, {
      explorer: true,
      customCss: '.swagger-ui .topbar { display: none }',
      customSiteTitle: 'University Schedule Assistant API',
    })
  );
};

export default specs;
