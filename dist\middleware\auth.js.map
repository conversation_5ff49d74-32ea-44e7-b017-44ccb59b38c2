{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAC/B,sDAA8C;AAC9C,4DAAoF;AAyB7E,MAAM,cAAc,GAAG,KAAK,EACjC,GAAY,EACZ,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QAEH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,MAAM,IAAI,kCAAmB,CAAC,mBAAmB,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAGtC,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,oBAAM,CAAC,GAAG,CAAC,MAAM,CAAe,CAAC;QAGnE,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,YAAY,EAAE,OAAO,CAAC,YAAY;SACnC,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAC3C,IAAI,CAAC,IAAI,kCAAmB,CAAC,eAAe,CAAC,CAAC,CAAC;QACjD,CAAC;aAAM,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAClD,IAAI,CAAC,IAAI,kCAAmB,CAAC,eAAe,CAAC,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,CAAC,CAAC;QACd,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AAnCW,QAAA,cAAc,kBAmCzB;AAGK,MAAM,WAAW,GAAG,CAAC,KAAe,EAAE,EAAE;IAC7C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC/D,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,IAAI,kCAAmB,CAAC,yBAAyB,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,iCAAkB,CAAC,0BAA0B,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAZW,QAAA,WAAW,eAYtB;AAGK,MAAM,iBAAiB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACzF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,IAAI,kCAAmB,CAAC,yBAAyB,CAAC,CAAC;IAC3D,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC9B,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC;IAGD,MAAM,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC,YAAY,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC;IACtE,IAAI,YAAY,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,KAAK,YAAY,EAAE,CAAC;QAC3D,MAAM,IAAI,iCAAkB,CAAC,kCAAkC,CAAC,CAAC;IACnE,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAjBW,QAAA,iBAAiB,qBAiB5B"}