#!/bin/bash

echo "Starting PostgreSQL and Redis with Docker..."

# Stop existing containers
docker stop schedule_assistant_db schedule_assistant_redis 2>/dev/null || true
docker rm schedule_assistant_db schedule_assistant_redis 2>/dev/null || true

# Start PostgreSQL
echo "Starting PostgreSQL..."
docker run -d \
  --name schedule_assistant_db \
  -e POSTGRES_DB=schedule_assistant \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -p 5432:5432 \
  postgres:15-alpine

# Start Redis
echo "Starting Redis..."
docker run -d \
  --name schedule_assistant_redis \
  -p 6379:6379 \
  redis:7-alpine

echo "Waiting for database to be ready..."
sleep 15

# Test database connection
echo "Testing database connection..."
if docker exec schedule_assistant_db pg_isready -U postgres -d schedule_assistant; then
    echo "✅ Database is ready!"
    echo "You can now run: npm run init"
else
    echo "❌ Database is not ready yet. Please wait a few more seconds and try again."
fi
