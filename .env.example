# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/schedule_assistant?schema=public"
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=schedule_assistant
DATABASE_USER=username
DATABASE_PASSWORD=password

# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Server Configuration
NODE_ENV=development
PORT=3000
API_VERSION=v1

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-refresh-token-secret
JWT_REFRESH_EXPIRES_IN=7d

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:5173
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=.xlsx,.xls,.csv

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# Scheduling Engine Configuration
SCHEDULING_TIMEOUT_MS=3600000
MAX_SCHEDULING_ITERATIONS=10000
OPTIMIZATION_LEVEL=high

# Background Jobs
BULL_REDIS_URL="redis://localhost:6379"
JOB_CONCURRENCY=5

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this

# API Documentation
SWAGGER_ENABLED=true
SWAGGER_PATH=/api-docs

# Monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true

# Development
DEBUG=schedule-assistant:*
