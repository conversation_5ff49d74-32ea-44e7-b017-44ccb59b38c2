@echo off
echo Starting PostgreSQL and Redis with Docker...

REM Stop existing containers
docker stop schedule_assistant_db schedule_assistant_redis 2>nul
docker rm schedule_assistant_db schedule_assistant_redis 2>nul

REM Start PostgreSQL
echo Starting PostgreSQL...
docker run -d ^
  --name schedule_assistant_db ^
  -e POSTGRES_DB=schedule_assistant ^
  -e POSTGRES_USER=postgres ^
  -e POSTGRES_PASSWORD=postgres ^
  -p 5432:5432 ^
  postgres:15-alpine

REM Start Redis
echo Starting Redis...
docker run -d ^
  --name schedule_assistant_redis ^
  -p 6379:6379 ^
  redis:7-alpine

echo Waiting for database to be ready...
timeout /t 15 /nobreak >nul

REM Test database connection
echo Testing database connection...
docker exec schedule_assistant_db pg_isready -U postgres -d schedule_assistant

if %errorlevel% equ 0 (
    echo ✅ Database is ready!
    echo You can now run: npm run init
) else (
    echo ❌ Database is not ready yet. Please wait a few more seconds and try again.
)

pause
