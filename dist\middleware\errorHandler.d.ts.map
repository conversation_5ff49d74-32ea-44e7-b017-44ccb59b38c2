{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAK1D,qBAAa,QAAS,SAAQ,KAAK;IAC1B,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,OAAO,CAAC;IACvB,IAAI,CAAC,EAAE,MAAM,CAAC;gBAET,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,aAAa,UAAO,EAAE,IAAI,CAAC,EAAE,MAAM;CAQrF;AAGD,qBAAa,eAAgB,SAAQ,QAAQ;IACpC,MAAM,EAAE,GAAG,EAAE,CAAC;gBAET,OAAO,EAAE,MAAM,EAAE,MAAM,GAAE,GAAG,EAAO;CAIhD;AAGD,qBAAa,uBAAwB,SAAQ,QAAQ;IAC5C,SAAS,EAAE,GAAG,EAAE,CAAC;gBAEZ,OAAO,EAAE,MAAM,EAAE,SAAS,GAAE,GAAG,EAAO;CAInD;AAGD,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,KAAK;CAMnD;AAGD,qBAAa,mBAAoB,SAAQ,QAAQ;gBACnC,OAAO,GAAE,MAAgC;CAGtD;AAGD,qBAAa,kBAAmB,SAAQ,QAAQ;gBAClC,OAAO,GAAE,MAAwB;CAG9C;AAGD,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,QAAQ,EAAE,MAAM;CAG7B;AAGD,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,EAAE,MAAM;CAG5B;AAGD,qBAAa,cAAe,SAAQ,QAAQ;gBAC9B,OAAO,GAAE,MAA4B;CAGlD;AAuFD,eAAO,MAAM,YAAY,GACvB,OAAO,KAAK,EACZ,KAAK,OAAO,EACZ,KAAK,QAAQ,EACb,MAAM,YAAY,KACjB,IAwCF,CAAC;AAGF,eAAO,MAAM,YAAY,GAAI,IAAI,QAAQ,MAC/B,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAGxD,CAAC;AAGF,eAAO,MAAM,eAAe,GAAI,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,IAGjF,CAAC;AAEF,eAAe,YAAY,CAAC"}