Write-Host "🚀 University Schedule Assistant - Ultimate Start" -ForegroundColor Cyan
Write-Host ""

# Stop ALL Docker containers
Write-Host "🛑 Stopping all Docker containers..." -ForegroundColor Yellow
$containers = docker ps -aq 2>$null
if ($containers) {
    docker stop $containers 2>$null
}

# Remove ALL Docker containers
Write-Host "🗑️ Removing all Docker containers..." -ForegroundColor Yellow
$containers = docker ps -aq 2>$null
if ($containers) {
    docker rm $containers 2>$null
}

Write-Host "✅ All containers cleaned up!" -ForegroundColor Green
Write-Host ""

# Start fresh PostgreSQL
Write-Host "🐘 Starting PostgreSQL..." -ForegroundColor Blue
docker run -d --name fresh_postgres -e POSTGRES_DB=schedule_assistant -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=postgres -p 5432:5432 postgres:15-alpine

# Start fresh Redis
Write-Host "🔴 Starting Redis..." -ForegroundColor Red
docker run -d --name fresh_redis -p 6379:6379 redis:7-alpine

Write-Host "⏳ Waiting for database to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# Test database connection
Write-Host "🔍 Testing database connection..." -ForegroundColor Blue
$dbReady = docker exec fresh_postgres pg_isready -U postgres -d schedule_assistant 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Database is ready!" -ForegroundColor Green
} else {
    Write-Host "❌ Database not ready, waiting more..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
}

# Setup database
Write-Host "🔧 Setting up database..." -ForegroundColor Blue
npx prisma generate
npx prisma migrate dev --name fresh_start

# Start the application
Write-Host "🚀 Starting application..." -ForegroundColor Green
Write-Host ""
Write-Host "📍 API: http://localhost:3000" -ForegroundColor Cyan
Write-Host "📖 Docs: http://localhost:3000/api-docs" -ForegroundColor Cyan
Write-Host "❤️ Health: http://localhost:3000/health" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press Ctrl+C to stop" -ForegroundColor Yellow
Write-Host ""

npx nodemon --exec "npx ts-node --transpile-only src/server.ts"
