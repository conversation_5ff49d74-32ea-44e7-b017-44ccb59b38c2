
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.SemesterScalarFieldEnum = {
  id: 'id',
  name: 'name',
  startDate: 'startDate',
  endDate: 'endDate',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DepartmentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  code: 'code',
  headInstructorId: 'headInstructorId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RoomScalarFieldEnum = {
  id: 'id',
  roomNumber: 'roomNumber',
  maxCapacity: 'maxCapacity',
  roomType: 'roomType',
  assignedDepartmentId: 'assignedDepartmentId',
  building: 'building',
  floorNumber: 'floorNumber',
  equipmentNotes: 'equipmentNotes',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InstructorScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  departmentId: 'departmentId',
  availability: 'availability',
  maxDailySlots: 'maxDailySlots',
  preferredTimes: 'preferredTimes',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StudentLevelScalarFieldEnum = {
  id: 'id',
  name: 'name',
  departmentId: 'departmentId',
  expectedStudentCount: 'expectedStudentCount',
  academicYear: 'academicYear',
  semesterId: 'semesterId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StudentGroupScalarFieldEnum = {
  id: 'id',
  levelId: 'levelId',
  groupName: 'groupName',
  parentGroupId: 'parentGroupId',
  groupType: 'groupType',
  studentCount: 'studentCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CourseScalarFieldEnum = {
  id: 'id',
  courseCode: 'courseCode',
  title: 'title',
  departmentId: 'departmentId',
  credits: 'credits',
  description: 'description',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LessonScalarFieldEnum = {
  id: 'id',
  courseId: 'courseId',
  studentGroupId: 'studentGroupId',
  instructorId: 'instructorId',
  durationInSlots: 'durationInSlots',
  lessonType: 'lessonType',
  requiredRoomType: 'requiredRoomType',
  semesterId: 'semesterId',
  weeklyFrequency: 'weeklyFrequency',
  totalSessions: 'totalSessions',
  isScheduled: 'isScheduled',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TimeSlotScalarFieldEnum = {
  slotNumber: 'slotNumber',
  startTime: 'startTime',
  endTime: 'endTime',
  displayLabel: 'displayLabel'
};

exports.Prisma.ScheduledLessonScalarFieldEnum = {
  id: 'id',
  lessonId: 'lessonId',
  roomId: 'roomId',
  dayOfWeek: 'dayOfWeek',
  startSlot: 'startSlot',
  endSlot: 'endSlot',
  weekNumber: 'weekNumber',
  semesterId: 'semesterId',
  isLocked: 'isLocked',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SchedulingRuleScalarFieldEnum = {
  id: 'id',
  ruleName: 'ruleName',
  ruleType: 'ruleType',
  constraintType: 'constraintType',
  ruleDefinition: 'ruleDefinition',
  priority: 'priority',
  isActive: 'isActive',
  semesterId: 'semesterId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SubstitutionRequestScalarFieldEnum = {
  id: 'id',
  originalScheduledLessonId: 'originalScheduledLessonId',
  absentInstructorId: 'absentInstructorId',
  substituteInstructorId: 'substituteInstructorId',
  substitutionDate: 'substitutionDate',
  reason: 'reason',
  status: 'status',
  requestedById: 'requestedById',
  approvedById: 'approvedById',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SchedulingSessionScalarFieldEnum = {
  id: 'id',
  semesterId: 'semesterId',
  sessionName: 'sessionName',
  status: 'status',
  manifestFilePath: 'manifestFilePath',
  generationStartedAt: 'generationStartedAt',
  generationCompletedAt: 'generationCompletedAt',
  totalLessons: 'totalLessons',
  scheduledLessons: 'scheduledLessons',
  unscheduledLessons: 'unscheduledLessons',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.RoomType = exports.$Enums.RoomType = {
  LECTURE_HALL: 'LECTURE_HALL',
  CLASSROOM: 'CLASSROOM',
  LAB: 'LAB',
  TUTORIAL_ROOM: 'TUTORIAL_ROOM',
  COMPUTER_LAB: 'COMPUTER_LAB'
};

exports.GroupType = exports.$Enums.GroupType = {
  FULL_LEVEL: 'FULL_LEVEL',
  GROUP: 'GROUP',
  SUB_GROUP: 'SUB_GROUP'
};

exports.LessonType = exports.$Enums.LessonType = {
  LECTURE: 'LECTURE',
  LAB: 'LAB',
  TUTORIAL: 'TUTORIAL',
  COMPUTER_LAB: 'COMPUTER_LAB',
  SEMINAR: 'SEMINAR',
  WORKSHOP: 'WORKSHOP'
};

exports.DayOfWeek = exports.$Enums.DayOfWeek = {
  MONDAY: 'MONDAY',
  TUESDAY: 'TUESDAY',
  WEDNESDAY: 'WEDNESDAY',
  THURSDAY: 'THURSDAY',
  FRIDAY: 'FRIDAY',
  SATURDAY: 'SATURDAY',
  SUNDAY: 'SUNDAY'
};

exports.RuleType = exports.$Enums.RuleType = {
  CONTIGUITY: 'CONTIGUITY',
  TIME_PLACEMENT: 'TIME_PLACEMENT',
  RESOURCE_USAGE: 'RESOURCE_USAGE',
  SPECIAL_COMBINATION: 'SPECIAL_COMBINATION'
};

exports.ConstraintType = exports.$Enums.ConstraintType = {
  HARD: 'HARD',
  SOFT: 'SOFT'
};

exports.SubstitutionStatus = exports.$Enums.SubstitutionStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  COMPLETED: 'COMPLETED'
};

exports.SchedulingSessionStatus = exports.$Enums.SchedulingSessionStatus = {
  DRAFT: 'DRAFT',
  MANIFEST_GENERATED: 'MANIFEST_GENERATED',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  PUBLISHED: 'PUBLISHED'
};

exports.Prisma.ModelName = {
  Semester: 'Semester',
  Department: 'Department',
  Room: 'Room',
  Instructor: 'Instructor',
  StudentLevel: 'StudentLevel',
  StudentGroup: 'StudentGroup',
  Course: 'Course',
  Lesson: 'Lesson',
  TimeSlot: 'TimeSlot',
  ScheduledLesson: 'ScheduledLesson',
  SchedulingRule: 'SchedulingRule',
  SubstitutionRequest: 'SubstitutionRequest',
  SchedulingSession: 'SchedulingSession'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
