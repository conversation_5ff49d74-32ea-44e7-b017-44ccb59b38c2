# University Schedule Assistant

A comprehensive university academic scheduling system with constraint-based optimization, inspired by professional scheduling software like aSc Timetables.

## Features

- **Comprehensive Data Management**: Manage semesters, departments, rooms, instructors, student groups, and courses
- **Intelligent Scheduling Engine**: Constraint-based algorithm with hard and soft constraints
- **Interactive Workflow**: Step-by-step process with Excel manifest generation and review
- **Real-time Conflict Detection**: Immediate feedback on scheduling conflicts
- **Substitution Management**: Handle daily teacher absences and replacements
- **Flexible Reporting**: Generate schedules for any resource (instructor, room, student group)
- **RESTful API**: Complete API for integration with other systems

## Technology Stack

- **Backend**: Node.js, Express.js, TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Cache**: Redis
- **Authentication**: JWT
- **File Processing**: ExcelJS
- **Documentation**: Swagger/OpenAPI
- **Containerization**: Docker

## Quick Start

### Prerequisites

- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Docker (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/university/schedule-assistant.git
   cd schedule-assistant
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up the database**
   ```bash
   # Run database migrations
   npm run db:migrate
   
   # Seed initial data
   npm run db:seed
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

### Using Docker

1. **Start all services**
   ```bash
   npm run docker:dev
   ```

2. **Run database migrations**
   ```bash
   docker exec -it schedule_assistant_app npm run db:migrate
   ```

## API Documentation

Once the server is running, visit:
- **API Documentation**: http://localhost:3000/api-docs
- **Health Check**: http://localhost:3000/health

## Project Structure

```
src/
├── config/          # Configuration files
├── controllers/     # Request handlers
├── middleware/      # Custom middleware
├── repositories/    # Data access layer
├── routes/          # API routes
├── services/        # Business logic
├── types/           # TypeScript definitions
├── utils/           # Utility functions
└── server.ts        # Application entry point

docs/
├── database-schema.sql      # Database schema
├── api-design.md           # API documentation
├── system-architecture.md  # System architecture
└── workflow-description.md # User workflow

prisma/
├── schema.prisma    # Database schema definition
├── migrations/      # Database migrations
└── seed.ts         # Database seeding
```

## Development Workflow

### 1. Data Setup
- Configure semesters, departments, and rooms
- Add instructor profiles with availability
- Set up student levels and groups
- Define courses and requirements

### 2. Lesson Generation
- System generates lesson instances automatically
- Review and validate generated lessons
- Download scheduling manifest Excel file

### 3. Schedule Generation
- Upload validated manifest (with optional modifications)
- Run constraint-based scheduling algorithm
- Resolve conflicts using interactive tools

### 4. Publishing
- Review final schedule
- Generate reports and exports
- Publish to web-based public view

## API Usage Examples

### Create a Semester
```bash
curl -X POST http://localhost:3000/api/v1/semesters \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Fall 2024",
    "start_date": "2024-09-01",
    "end_date": "2024-12-15"
  }'
```

### Generate Scheduling Manifest
```bash
curl -X POST http://localhost:3000/api/v1/scheduling/generate-manifest \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "semester_id": "uuid",
    "session_name": "Fall 2024 Schedule"
  }'
```

### Start Scheduling Process
```bash
curl -X POST http://localhost:3000/api/v1/scheduling/start \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "session_id": "uuid"
  }'
```

## Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## Deployment

### Production Build
```bash
npm run build
npm start
```

### Docker Production
```bash
npm run docker:prod
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: [API Docs](http://localhost:3000/api-docs)
- Issues: [GitHub Issues](https://github.com/university/schedule-assistant/issues)
