import { Router } from 'express';

// Import route modules (to be created)
// import authRoutes from './auth';
// import semesterRoutes from './semesters';
// import departmentRoutes from './departments';
// import roomRoutes from './rooms';
// import instructorRoutes from './instructors';
// import studentLevelRoutes from './studentLevels';
// import studentGroupRoutes from './studentGroups';
// import courseRoutes from './courses';
// import lessonRoutes from './lessons';
// import schedulingRoutes from './scheduling';
// import substitutionRoutes from './substitutions';
// import reportRoutes from './reports';

const publicRouter = Router();
const protectedRouter = Router();

// Public routes (no authentication required)
publicRouter.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'API is healthy',
    timestamp: new Date().toISOString(),
  });
});

// Protected routes (authentication required)
// protectedRouter.use('/auth', authRoutes);
// protectedRouter.use('/semesters', semesterRoutes);
// protectedRouter.use('/departments', departmentRoutes);
// protectedRouter.use('/rooms', roomRoutes);
// protectedRouter.use('/instructors', instructorRoutes);
// protectedRouter.use('/student-levels', studentLevelRoutes);
// protectedRouter.use('/student-groups', studentGroupRoutes);
// protectedRouter.use('/courses', courseRoutes);
// protectedRouter.use('/lessons', lessonRoutes);
// protectedRouter.use('/scheduling', schedulingRoutes);
// protectedRouter.use('/substitutions', substitutionRoutes);
// protectedRouter.use('/reports', reportRoutes);

export const routes = {
  public: publicRouter,
  protected: protectedRouter,
};
