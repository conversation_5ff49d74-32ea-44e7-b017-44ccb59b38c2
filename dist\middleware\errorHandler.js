"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notFoundHandler = exports.asyncHandler = exports.errorHandler = exports.RateLimitError = exports.ConflictError = exports.NotFoundError = exports.AuthorizationError = exports.AuthenticationError = exports.DatabaseError = exports.SchedulingConflictError = exports.ValidationError = exports.AppError = void 0;
const logger_1 = require("@/utils/logger");
const environment_1 = require("@/config/environment");
class AppError extends Error {
    constructor(message, statusCode, isOperational = true, code) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        this.code = code;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
class ValidationError extends AppError {
    constructor(message, errors = []) {
        super(message, 422, true, 'VALIDATION_ERROR');
        this.errors = errors;
    }
}
exports.ValidationError = ValidationError;
class SchedulingConflictError extends AppError {
    constructor(message, conflicts = []) {
        super(message, 409, true, 'SCHEDULING_CONFLICT');
        this.conflicts = conflicts;
    }
}
exports.SchedulingConflictError = SchedulingConflictError;
class DatabaseError extends AppError {
    constructor(message, originalError) {
        super(message, 500, true, 'DATABASE_ERROR');
        if (originalError) {
            this.stack = originalError.stack;
        }
    }
}
exports.DatabaseError = DatabaseError;
class AuthenticationError extends AppError {
    constructor(message = 'Authentication failed') {
        super(message, 401, true, 'AUTHENTICATION_ERROR');
    }
}
exports.AuthenticationError = AuthenticationError;
class AuthorizationError extends AppError {
    constructor(message = 'Access denied') {
        super(message, 403, true, 'AUTHORIZATION_ERROR');
    }
}
exports.AuthorizationError = AuthorizationError;
class NotFoundError extends AppError {
    constructor(resource) {
        super(`${resource} not found`, 404, true, 'NOT_FOUND');
    }
}
exports.NotFoundError = NotFoundError;
class ConflictError extends AppError {
    constructor(message) {
        super(message, 409, true, 'CONFLICT');
    }
}
exports.ConflictError = ConflictError;
class RateLimitError extends AppError {
    constructor(message = 'Too many requests') {
        super(message, 429, true, 'RATE_LIMIT_EXCEEDED');
    }
}
exports.RateLimitError = RateLimitError;
const handlePrismaError = (error) => {
    switch (error.code) {
        case 'P2002':
            return new ConflictError('A record with this data already exists');
        case 'P2025':
            return new NotFoundError('Record');
        case 'P2003':
            return new ValidationError('Foreign key constraint failed');
        case 'P2014':
            return new ValidationError('Invalid data provided');
        default:
            return new DatabaseError('Database operation failed', error);
    }
};
const handleJWTError = (error) => {
    if (error.name === 'JsonWebTokenError') {
        return new AuthenticationError('Invalid token');
    }
    if (error.name === 'TokenExpiredError') {
        return new AuthenticationError('Token expired');
    }
    return new AuthenticationError('Authentication failed');
};
const handleValidationError = (error) => {
    if (error.details) {
        const errors = error.details.map((detail) => ({
            field: detail.path.join('.'),
            message: detail.message,
            value: detail.context?.value,
        }));
        return new ValidationError('Validation failed', errors);
    }
    return new ValidationError('Validation failed');
};
const sendErrorResponse = (error, req, res) => {
    const errorResponse = {
        success: false,
        message: error.message,
        error: error.constructor.name,
        code: error.code,
        timestamp: new Date().toISOString(),
        path: req.originalUrl,
        method: req.method,
    };
    if (error instanceof ValidationError) {
        errorResponse.errors = error.errors;
    }
    if (error instanceof SchedulingConflictError) {
        errorResponse.conflicts = error.conflicts;
    }
    if (environment_1.config.nodeEnv === 'development') {
        errorResponse.stack = error.stack;
    }
    res.status(error.statusCode).json(errorResponse);
};
const errorHandler = (error, req, res, next) => {
    let appError;
    if (error instanceof AppError) {
        appError = error;
    }
    else if (error.name?.startsWith('Prisma')) {
        appError = handlePrismaError(error);
    }
    else if (error.name?.includes('JsonWebToken') || error.name?.includes('TokenExpired')) {
        appError = handleJWTError(error);
    }
    else if (error.name === 'ValidationError') {
        appError = handleValidationError(error);
    }
    else {
        appError = new AppError(environment_1.config.nodeEnv === 'production' ? 'Internal server error' : error.message, 500, false);
    }
    logger_1.logger.error('Error occurred', {
        error: {
            name: error.name,
            message: error.message,
            stack: error.stack,
        },
        request: {
            method: req.method,
            url: req.originalUrl,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
        },
        statusCode: appError.statusCode,
        isOperational: appError.isOperational,
    });
    sendErrorResponse(appError, req, res);
};
exports.errorHandler = errorHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
const notFoundHandler = (req, res, next) => {
    const error = new NotFoundError(`Route ${req.originalUrl}`);
    next(error);
};
exports.notFoundHandler = notFoundHandler;
exports.default = exports.errorHandler;
//# sourceMappingURL=errorHandler.js.map