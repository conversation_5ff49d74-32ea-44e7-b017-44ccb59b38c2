export declare const config: {
    readonly nodeEnv: any;
    readonly port: any;
    readonly apiVersion: any;
    readonly database: {
        readonly url: any;
        readonly host: any;
        readonly port: any;
        readonly name: any;
        readonly user: any;
        readonly password: any;
    };
    readonly redis: {
        readonly url: any;
        readonly host: any;
        readonly port: any;
        readonly password: any;
    };
    readonly jwt: {
        readonly secret: any;
        readonly expiresIn: any;
        readonly refreshSecret: any;
        readonly refreshExpiresIn: any;
    };
    readonly cors: {
        readonly origin: any;
        readonly credentials: any;
    };
    readonly rateLimit: {
        readonly windowMs: any;
        readonly maxRequests: any;
    };
    readonly upload: {
        readonly maxFileSize: any;
        readonly uploadPath: any;
        readonly allowedFileTypes: any;
    };
    readonly email: {
        readonly host: any;
        readonly port: any;
        readonly secure: any;
        readonly user: any;
        readonly password: any;
        readonly from: any;
    };
    readonly logging: {
        readonly level: any;
        readonly filePath: any;
        readonly maxSize: any;
        readonly maxFiles: any;
    };
    readonly scheduling: {
        readonly timeoutMs: any;
        readonly maxIterations: any;
        readonly optimizationLevel: any;
    };
    readonly jobs: {
        readonly redisUrl: any;
        readonly concurrency: any;
    };
    readonly security: {
        readonly bcryptRounds: any;
        readonly sessionSecret: any;
    };
    readonly swagger: {
        readonly enabled: any;
        readonly path: any;
    };
    readonly monitoring: {
        readonly healthCheckEnabled: any;
        readonly metricsEnabled: any;
    };
};
export type Config = typeof config;
//# sourceMappingURL=environment.d.ts.map