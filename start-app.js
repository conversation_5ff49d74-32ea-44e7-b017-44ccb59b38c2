#!/usr/bin/env node

/**
 * University Schedule Assistant - Application Startup Script
 * 
 * This script initializes and starts the University Schedule Assistant application
 * with proper environment validation, dependency checks, and graceful error handling.
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const readline = require('readline');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Application configuration
const APP_CONFIG = {
  name: 'University Schedule Assistant',
  version: '1.0.0',
  nodeVersion: '18.0.0',
  requiredFiles: [
    'package.json',
    'tsconfig.json',
    '.env.example',
    'src/server.ts',
    'prisma/schema.prisma'
  ],
  requiredDirs: [
    'src',
    'prisma',
    'docs'
  ]
};

class AppInitializer {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  // Utility methods
  log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  logStep(step, message) {
    this.log(`\n[${step}] ${message}`, 'cyan');
  }

  logSuccess(message) {
    this.log(`✅ ${message}`, 'green');
  }

  logError(message) {
    this.log(`❌ ${message}`, 'red');
  }

  logWarning(message) {
    this.log(`⚠️  ${message}`, 'yellow');
  }

  logInfo(message) {
    this.log(`ℹ️  ${message}`, 'blue');
  }

  async question(prompt) {
    return new Promise((resolve) => {
      this.rl.question(`${colors.yellow}${prompt}${colors.reset}`, resolve);
    });
  }

  // Check if Node.js version meets requirements
  checkNodeVersion() {
    const currentVersion = process.version.slice(1); // Remove 'v' prefix
    const requiredVersion = APP_CONFIG.nodeVersion;
    
    if (this.compareVersions(currentVersion, requiredVersion) < 0) {
      throw new Error(`Node.js ${requiredVersion} or higher is required. Current version: ${currentVersion}`);
    }
    
    this.logSuccess(`Node.js version ${currentVersion} meets requirements`);
  }

  compareVersions(version1, version2) {
    const v1parts = version1.split('.').map(Number);
    const v2parts = version2.split('.').map(Number);
    
    for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
      const v1part = v1parts[i] || 0;
      const v2part = v2parts[i] || 0;
      
      if (v1part > v2part) return 1;
      if (v1part < v2part) return -1;
    }
    
    return 0;
  }

  // Check if required files and directories exist
  checkProjectStructure() {
    this.logStep('1', 'Checking project structure...');
    
    // Check required directories
    for (const dir of APP_CONFIG.requiredDirs) {
      if (!fs.existsSync(dir)) {
        throw new Error(`Required directory missing: ${dir}`);
      }
    }
    
    // Check required files
    for (const file of APP_CONFIG.requiredFiles) {
      if (!fs.existsSync(file)) {
        throw new Error(`Required file missing: ${file}`);
      }
    }
    
    this.logSuccess('Project structure is valid');
  }

  // Check and install dependencies
  async checkDependencies() {
    this.logStep('2', 'Checking dependencies...');
    
    if (!fs.existsSync('node_modules')) {
      this.logInfo('node_modules not found. Installing dependencies...');
      
      try {
        execSync('npm install', { stdio: 'inherit' });
        this.logSuccess('Dependencies installed successfully');
      } catch (error) {
        throw new Error('Failed to install dependencies');
      }
    } else {
      this.logSuccess('Dependencies are already installed');
    }
  }

  // Setup environment configuration
  async setupEnvironment() {
    this.logStep('3', 'Setting up environment configuration...');
    
    if (!fs.existsSync('.env')) {
      this.logWarning('.env file not found');
      
      const createEnv = await this.question('Would you like to create .env from .env.example? (y/n): ');
      
      if (createEnv.toLowerCase() === 'y' || createEnv.toLowerCase() === 'yes') {
        fs.copyFileSync('.env.example', '.env');
        this.logSuccess('.env file created from .env.example');
        this.logWarning('Please edit .env file with your configuration before continuing');
        
        const continueSetup = await this.question('Have you configured .env file? (y/n): ');
        if (continueSetup.toLowerCase() !== 'y' && continueSetup.toLowerCase() !== 'yes') {
          this.logInfo('Please configure .env file and run the script again');
          process.exit(0);
        }
      } else {
        throw new Error('.env file is required for application startup');
      }
    } else {
      this.logSuccess('.env file exists');
    }
  }

  // Check if Docker is available
  checkDockerAvailability() {
    try {
      execSync('docker --version', { stdio: 'pipe' });
      return true;
    } catch (error) {
      return false;
    }
  }

  // Setup database - with Docker option
  async setupDatabase() {
    this.logStep('4', 'Setting up database...');

    // Check if database is already running
    try {
      execSync('npx prisma db push --preview-feature', { stdio: 'pipe' });
      this.logSuccess('Database connection verified - already running');
      await this.runDatabaseMigrations();
      return;
    } catch (error) {
      this.logInfo('Database not accessible, need to start database service...');
    }

    // Check if Docker is available
    const hasDocker = this.checkDockerAvailability();

    if (hasDocker) {
      this.logInfo('Docker detected. Would you like to use Docker for the database?');
      const useDocker = await this.question('Use Docker for PostgreSQL and Redis? (y/n): ');

      if (useDocker.toLowerCase() === 'y' || useDocker.toLowerCase() === 'yes') {
        await this.startDatabaseWithDocker();
        return;
      }
    }

    // Manual database setup
    this.logWarning('Please ensure PostgreSQL is installed and running manually.');
    this.logInfo('You can:');
    this.logInfo('1. Install PostgreSQL from https://www.postgresql.org/download/');
    this.logInfo('2. Or install Docker and restart this script');
    this.logInfo('3. Or use a cloud database (Supabase, Railway, etc.)');

    const continueAnyway = await this.question('Continue with manual database setup? (y/n): ');
    if (continueAnyway.toLowerCase() === 'y' || continueAnyway.toLowerCase() === 'yes') {
      await this.runDatabaseMigrations();
    } else {
      throw new Error('Database setup cancelled. Please set up PostgreSQL and try again.');
    }
  }

  // Start database with Docker
  async startDatabaseWithDocker() {
    this.logInfo('Starting PostgreSQL and Redis with Docker...');

    try {
      // Stop existing containers
      try {
        execSync('docker stop schedule_assistant_db schedule_assistant_redis', { stdio: 'pipe' });
        execSync('docker rm schedule_assistant_db schedule_assistant_redis', { stdio: 'pipe' });
      } catch (error) {
        // Containers might not exist, that's okay
      }

      // Start PostgreSQL
      this.logInfo('Starting PostgreSQL container...');
      execSync(`docker run -d \
        --name schedule_assistant_db \
        -e POSTGRES_DB=schedule_assistant \
        -e POSTGRES_USER=postgres \
        -e POSTGRES_PASSWORD=postgres \
        -p 5432:5432 \
        postgres:15-alpine`, { stdio: 'inherit' });

      // Start Redis
      this.logInfo('Starting Redis container...');
      execSync(`docker run -d \
        --name schedule_assistant_redis \
        -p 6379:6379 \
        redis:7-alpine`, { stdio: 'inherit' });

      // Wait for database to be ready
      this.logInfo('Waiting for database to be ready...');
      await this.waitForDatabase();

      this.logSuccess('Database containers started successfully');
      await this.runDatabaseMigrations();

    } catch (error) {
      throw new Error(`Failed to start database with Docker: ${error.message}`);
    }
  }

  // Wait for database to be ready
  async waitForDatabase(maxAttempts = 30) {
    for (let i = 0; i < maxAttempts; i++) {
      try {
        execSync('docker exec schedule_assistant_db pg_isready -U postgres -d schedule_assistant', { stdio: 'pipe' });
        this.logSuccess('Database is ready!');
        return;
      } catch (error) {
        this.logInfo(`Waiting for database... (${i + 1}/${maxAttempts})`);
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      }
    }
    throw new Error('Database failed to become ready within timeout period');
  }

  // Run database migrations
  async runDatabaseMigrations() {
    try {
      // Generate Prisma client
      this.logInfo('Generating Prisma client...');
      execSync('npx prisma generate', { stdio: 'inherit' });
      this.logSuccess('Prisma client generated');

      // Run migrations
      this.logInfo('Running database migrations...');
      execSync('npx prisma migrate dev --name init', { stdio: 'inherit' });
      this.logSuccess('Database migration completed');

      const seedDatabase = await this.question('Would you like to seed the database with sample data? (y/n): ');

      if (seedDatabase.toLowerCase() === 'y' || seedDatabase.toLowerCase() === 'yes') {
        this.logInfo('Seeding database...');
        execSync('npx prisma db seed', { stdio: 'inherit' });
        this.logSuccess('Database seeded with sample data');
      }
    } catch (error) {
      throw new Error(`Database migration failed: ${error.message}`);
    }
  }

  // Build the application
  async buildApplication() {
    this.logStep('5', 'Building application...');
    
    try {
      this.logInfo('Compiling TypeScript...');
      execSync('npm run build', { stdio: 'inherit' });
      this.logSuccess('Application built successfully');
    } catch (error) {
      throw new Error(`Build failed: ${error.message}`);
    }
  }

  // Run health check
  async runHealthCheck() {
    this.logStep('6', 'Running health check...');

    try {
      const HealthChecker = require('./scripts/health-check.js');
      const checker = new HealthChecker();
      const isHealthy = await checker.runAllChecks();

      if (isHealthy) {
        this.logSuccess('All health checks passed!');
        return true;
      } else {
        this.logWarning('Some health checks failed. Continue anyway?');
        const continueAnyway = await this.question('Continue starting the application? (y/n): ');
        return continueAnyway.toLowerCase() === 'y' || continueAnyway.toLowerCase() === 'yes';
      }
    } catch (error) {
      this.logWarning(`Health check failed: ${error.message}`);
      const continueAnyway = await this.question('Continue starting the application? (y/n): ');
      return continueAnyway.toLowerCase() === 'y' || continueAnyway.toLowerCase() === 'yes';
    }
  }

  // Start the application
  async startApplication() {
    this.logStep('7', 'Starting application...');

    const mode = await this.question('Start in development mode? (y/n): ');

    if (mode.toLowerCase() === 'y' || mode.toLowerCase() === 'yes') {
      this.logInfo('Starting in development mode...');
      this.logSuccess('Application is starting...');
      this.logInfo('API Documentation: http://localhost:3000/api-docs');
      this.logInfo('Health Check: http://localhost:3000/health');
      this.logInfo('Manual Health Check: npm run health');
      this.logInfo('Press Ctrl+C to stop the application');

      // Start development server
      const devProcess = spawn('npm', ['run', 'dev'], { stdio: 'inherit' });

      // Handle process termination
      process.on('SIGINT', () => {
        this.log('\nShutting down application...', 'yellow');
        devProcess.kill('SIGINT');
        process.exit(0);
      });

    } else {
      this.logInfo('Starting in production mode...');
      this.logSuccess('Application is starting...');

      // Start production server
      const prodProcess = spawn('npm', ['start'], { stdio: 'inherit' });

      // Handle process termination
      process.on('SIGINT', () => {
        this.log('\nShutting down application...', 'yellow');
        prodProcess.kill('SIGINT');
        process.exit(0);
      });
    }
  }

  // Display welcome message
  displayWelcome() {
    console.clear();
    this.log('╔══════════════════════════════════════════════════════════════╗', 'cyan');
    this.log('║                                                              ║', 'cyan');
    this.log('║            University Schedule Assistant                     ║', 'bright');
    this.log('║                                                              ║', 'cyan');
    this.log('║     Comprehensive Academic Scheduling System                ║', 'cyan');
    this.log('║                                                              ║', 'cyan');
    this.log('╚══════════════════════════════════════════════════════════════╝', 'cyan');
    this.log(`\nVersion: ${APP_CONFIG.version}`, 'blue');
    this.log('Initializing application...\n', 'blue');
  }

  // Display completion message
  displayCompletion() {
    this.log('\n╔══════════════════════════════════════════════════════════════╗', 'green');
    this.log('║                                                              ║', 'green');
    this.log('║                 🎉 SETUP COMPLETED! 🎉                      ║', 'bright');
    this.log('║                                                              ║', 'green');
    this.log('║     University Schedule Assistant is ready to use!          ║', 'green');
    this.log('║                                                              ║', 'green');
    this.log('╚══════════════════════════════════════════════════════════════╝', 'green');
  }

  // Main initialization process
  async initialize() {
    try {
      this.displayWelcome();
      
      // Run initialization steps
      this.checkNodeVersion();
      this.checkProjectStructure();
      await this.checkDependencies();
      await this.setupEnvironment();
      await this.setupDatabase();
      await this.buildApplication();

      // Run health check before starting
      const healthCheckPassed = await this.runHealthCheck();
      if (!healthCheckPassed) {
        this.logError('Health check failed. Please fix the issues and try again.');
        process.exit(1);
      }

      this.displayCompletion();
      await this.startApplication();
      
    } catch (error) {
      this.logError(`Initialization failed: ${error.message}`);
      this.logInfo('Please fix the issue and run the script again');
      process.exit(1);
    } finally {
      this.rl.close();
    }
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error(`${colors.red}❌ Uncaught Exception: ${error.message}${colors.reset}`);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(`${colors.red}❌ Unhandled Rejection at:${colors.reset}`, promise, `${colors.red}reason:${colors.reset}`, reason);
  process.exit(1);
});

// Start the application
if (require.main === module) {
  const initializer = new AppInitializer();
  initializer.initialize();
}

module.exports = AppInitializer;
