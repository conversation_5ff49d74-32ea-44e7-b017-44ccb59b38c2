import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';
import { config } from '@/config/environment';

// Custom error class
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;

  constructor(message: string, statusCode: number, isOperational = true, code?: string) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Validation error class
export class ValidationError extends AppError {
  public errors: any[];

  constructor(message: string, errors: any[] = []) {
    super(message, 422, true, 'VALIDATION_ERROR');
    this.errors = errors;
  }
}

// Scheduling conflict error class
export class SchedulingConflictError extends AppError {
  public conflicts: any[];

  constructor(message: string, conflicts: any[] = []) {
    super(message, 409, true, 'SCHEDULING_CONFLICT');
    this.conflicts = conflicts;
  }
}

// Database error class
export class DatabaseError extends AppError {
  constructor(message: string, originalError?: Error) {
    super(message, 500, true, 'DATABASE_ERROR');
    if (originalError) {
      this.stack = originalError.stack;
    }
  }
}

// Authentication error class
export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401, true, 'AUTHENTICATION_ERROR');
  }
}

// Authorization error class
export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied') {
    super(message, 403, true, 'AUTHORIZATION_ERROR');
  }
}

// Not found error class
export class NotFoundError extends AppError {
  constructor(resource: string) {
    super(`${resource} not found`, 404, true, 'NOT_FOUND');
  }
}

// Conflict error class
export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409, true, 'CONFLICT');
  }
}

// Rate limit error class
export class RateLimitError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, true, 'RATE_LIMIT_EXCEEDED');
  }
}

// Error response interface
interface ErrorResponse {
  success: false;
  message: string;
  error: string;
  code?: string;
  errors?: any[];
  conflicts?: any[];
  stack?: string;
  timestamp: string;
  path: string;
  method: string;
}

// Handle Prisma errors
const handlePrismaError = (error: any): AppError => {
  switch (error.code) {
    case 'P2002':
      return new ConflictError('A record with this data already exists');
    case 'P2025':
      return new NotFoundError('Record');
    case 'P2003':
      return new ValidationError('Foreign key constraint failed');
    case 'P2014':
      return new ValidationError('Invalid data provided');
    default:
      return new DatabaseError('Database operation failed', error);
  }
};

// Handle JWT errors
const handleJWTError = (error: any): AppError => {
  if (error.name === 'JsonWebTokenError') {
    return new AuthenticationError('Invalid token');
  }
  if (error.name === 'TokenExpiredError') {
    return new AuthenticationError('Token expired');
  }
  return new AuthenticationError('Authentication failed');
};

// Handle validation errors
const handleValidationError = (error: any): AppError => {
  if (error.details) {
    // Joi validation error
    const errors = error.details.map((detail: any) => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value,
    }));
    return new ValidationError('Validation failed', errors);
  }
  return new ValidationError('Validation failed');
};

// Send error response
const sendErrorResponse = (error: AppError, req: Request, res: Response): void => {
  const errorResponse: ErrorResponse = {
    success: false,
    message: error.message,
    error: error.constructor.name,
    code: error.code,
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    method: req.method,
  };

  // Add specific error details
  if (error instanceof ValidationError) {
    errorResponse.errors = error.errors;
  }

  if (error instanceof SchedulingConflictError) {
    errorResponse.conflicts = error.conflicts;
  }

  // Include stack trace in development
  if (config.nodeEnv === 'development') {
    errorResponse.stack = error.stack;
  }

  res.status(error.statusCode).json(errorResponse);
};

// Main error handler middleware
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let appError: AppError;

  // Convert known errors to AppError
  if (error instanceof AppError) {
    appError = error;
  } else if (error.name?.startsWith('Prisma')) {
    appError = handlePrismaError(error);
  } else if (error.name?.includes('JsonWebToken') || error.name?.includes('TokenExpired')) {
    appError = handleJWTError(error);
  } else if (error.name === 'ValidationError') {
    appError = handleValidationError(error);
  } else {
    // Unknown error - treat as internal server error
    appError = new AppError(
      config.nodeEnv === 'production' ? 'Internal server error' : error.message,
      500,
      false
    );
  }

  // Log error
  logger.error('Error occurred', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    },
    statusCode: appError.statusCode,
    isOperational: appError.isOperational,
  });

  // Send error response
  sendErrorResponse(appError, req, res);
};

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 404 handler
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new NotFoundError(`Route ${req.originalUrl}`);
  next(error);
};

export default errorHandler;
