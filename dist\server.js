"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
const requestLogger_1 = require("@/middleware/requestLogger");
const auth_1 = require("@/middleware/auth");
const swagger_1 = require("@/config/swagger");
const database_1 = require("@/config/database");
const redis_1 = require("@/config/redis");
const routes_1 = require("@/routes");
class Server {
    constructor() {
        this.app = (0, express_1.default)();
        this.port = environment_1.config.port;
        this.initializeMiddleware();
        this.initializeRoutes();
        this.initializeErrorHandling();
    }
    initializeMiddleware() {
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                },
            },
        }));
        this.app.use((0, cors_1.default)({
            origin: environment_1.config.cors.origin,
            credentials: environment_1.config.cors.credentials,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
        }));
        this.app.use((0, compression_1.default)());
        const limiter = (0, express_rate_limit_1.default)({
            windowMs: environment_1.config.rateLimit.windowMs,
            max: environment_1.config.rateLimit.maxRequests,
            message: {
                error: 'Too many requests from this IP, please try again later.',
            },
            standardHeaders: true,
            legacyHeaders: false,
        });
        this.app.use('/api', limiter);
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
        this.app.use(requestLogger_1.requestLogger);
        this.app.get('/health', (req, res) => {
            res.status(200).json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: environment_1.config.nodeEnv,
            });
        });
        if (environment_1.config.swagger.enabled) {
            (0, swagger_1.setupSwagger)(this.app);
        }
    }
    initializeRoutes() {
        this.app.use('/api/v1/public', routes_1.routes.public);
        this.app.use('/api/v1', auth_1.authMiddleware, routes_1.routes.protected);
        this.app.use('*', (req, res) => {
            res.status(404).json({
                success: false,
                message: `Route ${req.originalUrl} not found`,
                error: 'Not Found',
            });
        });
    }
    initializeErrorHandling() {
        this.app.use(errorHandler_1.errorHandler);
    }
    async start() {
        try {
            await (0, database_1.connectDatabase)();
            logger_1.logger.info('Database connected successfully');
            await (0, redis_1.connectRedis)();
            logger_1.logger.info('Redis connected successfully');
            this.app.listen(this.port, () => {
                logger_1.logger.info(`Server running on port ${this.port} in ${environment_1.config.nodeEnv} mode`);
                logger_1.logger.info(`API documentation available at http://localhost:${this.port}/api-docs`);
            });
            this.setupGracefulShutdown();
        }
        catch (error) {
            logger_1.logger.error('Failed to start server:', error);
            process.exit(1);
        }
    }
    setupGracefulShutdown() {
        const gracefulShutdown = (signal) => {
            logger_1.logger.info(`Received ${signal}. Starting graceful shutdown...`);
            process.exit(0);
        };
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    }
}
const server = new Server();
server.start().catch((error) => {
    logger_1.logger.error('Failed to start application:', error);
    process.exit(1);
});
exports.default = server;
//# sourceMappingURL=server.js.map