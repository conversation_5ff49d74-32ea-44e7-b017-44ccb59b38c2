#!/usr/bin/env node

/**
 * Quick Start Script - Simplified version
 * Gets the app running with minimal configuration
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkDocker() {
  try {
    execSync('docker --version', { stdio: 'pipe' });
    return true;
  } catch (error) {
    return false;
  }
}

function checkNodeModules() {
  return fs.existsSync('./node_modules');
}

function checkEnvFile() {
  return fs.existsSync('./.env');
}

async function startDatabaseWithDocker() {
  log('🐳 Starting database with Docker...', 'blue');
  
  try {
    // Stop existing containers
    try {
      execSync('docker stop schedule_assistant_db schedule_assistant_redis 2>/dev/null || true', { stdio: 'pipe' });
      execSync('docker rm schedule_assistant_db schedule_assistant_redis 2>/dev/null || true', { stdio: 'pipe' });
    } catch (error) {
      // Ignore errors
    }

    // Start PostgreSQL
    log('Starting PostgreSQL...', 'cyan');
    execSync(`docker run -d --name schedule_assistant_db -e POSTGRES_DB=schedule_assistant -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=postgres -p 5432:5432 postgres:15-alpine`, { stdio: 'inherit' });

    // Start Redis
    log('Starting Redis...', 'cyan');
    execSync(`docker run -d --name schedule_assistant_redis -p 6379:6379 redis:7-alpine`, { stdio: 'inherit' });

    // Wait for database
    log('Waiting for database to be ready...', 'yellow');
    for (let i = 0; i < 30; i++) {
      try {
        execSync('docker exec schedule_assistant_db pg_isready -U postgres -d schedule_assistant', { stdio: 'pipe' });
        log('✅ Database is ready!', 'green');
        return true;
      } catch (error) {
        process.stdout.write('.');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    throw new Error('Database failed to start within timeout');
  } catch (error) {
    log(`❌ Failed to start database: ${error.message}`, 'red');
    return false;
  }
}

async function main() {
  console.clear();
  log('╔══════════════════════════════════════════════════════════════╗', 'cyan');
  log('║                                                              ║', 'cyan');
  log('║            University Schedule Assistant                     ║', 'cyan');
  log('║                   Quick Start                                ║', 'cyan');
  log('║                                                              ║', 'cyan');
  log('╚══════════════════════════════════════════════════════════════╝', 'cyan');
  
  // Step 1: Check dependencies
  log('\n📦 Checking dependencies...', 'blue');
  if (!checkNodeModules()) {
    log('Installing dependencies...', 'yellow');
    execSync('npm install', { stdio: 'inherit' });
  }
  log('✅ Dependencies ready', 'green');

  // Step 2: Check environment
  log('\n⚙️  Checking environment...', 'blue');
  if (!checkEnvFile()) {
    log('Creating .env file...', 'yellow');
    fs.copyFileSync('.env.example', '.env');
    log('✅ .env file created', 'green');
  } else {
    log('✅ .env file exists', 'green');
  }

  // Step 3: Check database
  log('\n🗄️  Setting up database...', 'blue');
  
  // Try to connect to existing database
  try {
    execSync('npx prisma db push --preview-feature --accept-data-loss', { stdio: 'pipe' });
    log('✅ Database already running and accessible', 'green');
  } catch (error) {
    // Database not accessible, try Docker
    if (checkDocker()) {
      log('Database not accessible, starting with Docker...', 'yellow');
      const dbStarted = await startDatabaseWithDocker();
      if (!dbStarted) {
        log('❌ Failed to start database with Docker', 'red');
        log('Please install PostgreSQL manually or fix Docker setup', 'yellow');
        process.exit(1);
      }
    } else {
      log('❌ Docker not available and database not accessible', 'red');
      log('Please install Docker or PostgreSQL manually', 'yellow');
      process.exit(1);
    }
  }

  // Step 4: Run migrations
  log('\n🔄 Running database setup...', 'blue');
  try {
    execSync('npx prisma generate', { stdio: 'inherit' });
    execSync('npx prisma migrate dev --name init', { stdio: 'inherit' });
    execSync('npx prisma db seed', { stdio: 'inherit' });
    log('✅ Database setup complete', 'green');
  } catch (error) {
    log('⚠️  Database setup had issues, but continuing...', 'yellow');
  }

  // Step 5: Build and start
  log('\n🔨 Building application...', 'blue');
  try {
    execSync('npm run build', { stdio: 'inherit' });
    log('✅ Build complete', 'green');
  } catch (error) {
    log('⚠️  Build had issues, starting in development mode...', 'yellow');
  }

  // Step 6: Start the application
  log('\n🚀 Starting application...', 'green');
  log('API will be available at: http://localhost:3000', 'cyan');
  log('API Documentation: http://localhost:3000/api-docs', 'cyan');
  log('Press Ctrl+C to stop\n', 'yellow');

  const devProcess = spawn('npm', ['run', 'dev'], { stdio: 'inherit' });
  
  process.on('SIGINT', () => {
    log('\n👋 Shutting down...', 'yellow');
    devProcess.kill('SIGINT');
    process.exit(0);
  });
}

main().catch(error => {
  log(`❌ Quick start failed: ${error.message}`, 'red');
  process.exit(1);
});
